<template>
  <div h-screen flex>
    <div v-if="isShowInput" hidden md:block w-200px bg-white border-r border-gray-200 py-10px relative>
      <div h-60px flex items-center justify-center border-b border-gray-200>
        <img
          src="/qh_logo.png"
          alt="青禾AI"
          class="w-60px h-60px cursor-pointer"
          @click="$router.push('/')"
        />
      </div>
      <el-menu class="el-menu-vertical" :default-active="route.path" router>
        <el-menu-item index="/">
          <el-icon><Grid /></el-icon>
          <span>应用</span>
        </el-menu-item>
        <el-menu-item index="/chat">
          <el-icon><ChatDotRound /></el-icon>
          <span>立即聊天</span>
        </el-menu-item>
        <el-menu-item index="/deepSeekR1">
          <span class="text-18px mr-10px"> 🤔 </span>
          <span>DeepSeek R1</span>
        </el-menu-item>
        <el-menu-item index="/app/mindMap">
          <span class="text-18px mr-10px"> 📝 </span>
          <span>思维导图</span>
        </el-menu-item>
        <el-menu-item index="/user">
          <el-icon><User /></el-icon>
          <span>我的</span>
        </el-menu-item>
      </el-menu>

      <div w-full absolute bottom-30px>
        <div v-if="!isLogin" class="flex justify-center">
          <el-button type="primary" class="w-120px" @click="handleLogin">登录</el-button>
        </div>
        <div v-else>
          <el-popover
            placement="top-start"
            trigger="hover"
            popper-class="popover-class"
            :show-arrow="false"
          >
            <template #reference>
              <div
                w-150px
                mx-auto
                flex
                items-center
                justify-center
                cursor-pointer
                hover:bg-sky-100
                py-10px
                px-6px
                rounded-24px
              >
                <div class="i-uiw:user text-12px text-sky-500 mr-10px"></div>
                {{ userInfo.nickName }}
              </div>
            </template>
            <div
              text-red-4
              cursor-pointer
              hover:bg-red-1
              h-30px
              rounded-md
              flex
              items-center
              justify-center
              @click="logout"
            >
              <div i-uiw-poweroff text-12px text-red-4 mr-5px></div>
              退出登录
            </div>
          </el-popover>
        </div>
      </div>
    </div>

    <div class="flex-1 flex flex-col overflow-hidden bg-slate-100">
      <div class="h-full overflow-auto main">
        <div class="h-full px-20px">
          <router-view></router-view>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { House, ChatDotRound, User, Grid } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { useLogin } from '@/plugins/loginDialog'
import { ref, onMounted, onUnmounted } from 'vue'
import { hasToken, removeToken } from '@/utils/token'
import emitter from '@/utils/eventBus'
const { showLogin } = useLogin()

const route = useRoute()
const router = useRouter()

const isShowInput = ref(route.query.type !== 'ylf')

const isLogin = ref(false)
// 路由拦截 /chat 判断是否登录
router.beforeEach((to, from, next) => {
  if (to.path === '/chat' && !hasToken()) {
    showLogin({
      onSuccess: () => {
        isLogin.value = true
        next()
      }
    })
  } else {
    next()
  }
})
const userInfo = ref<any>({})

if (hasToken()) {
  isLogin.value = true
  userInfo.value = JSON.parse(localStorage.getItem('USER_INFO') || '{}')
}
const logout = () => {
  localStorage.removeItem('USER_INFO')
  removeToken()
  isLogin.value = false
  emitter.emit('logout')
  ElMessage.success('退出成功')
  router.push('/')
}
const handleLogin = () => {
  showLogin({
    onSuccess: () => {
      isLogin.value = true
      userInfo.value = JSON.parse(localStorage.getItem('USER_INFO') || '{}')
      emitter.emit('login')
    }
  })
}

// 添加事件监听
onMounted(() => {
  emitter.on('logout', () => {
    isLogin.value = false
    userInfo.value = {}
  })
  emitter.on('login', () => {
    isLogin.value = true
    userInfo.value = JSON.parse(localStorage.getItem('USER_INFO') || '{}')
  })
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  emitter.off('logout')
  emitter.off('login')
})
</script>

<style scoped>
.el-menu-vertical {
  border-right: none;
}
</style>
<style>
.popover-class {
  padding: 5px !important;
  border-radius: 10px !important;
}
</style>
