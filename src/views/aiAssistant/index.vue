<template>
  <div class="page-container max-w-950px mx-auto">
    <div class="fixed-header">
      <div class="header">
        <div class="title-section">
          <div class="search-box">
            <el-input
              v-model="pageParams.keyword"
              placeholder="搜索应用..."
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
            />
          </div>
        </div>
      </div>

      <!-- 顶部分类导航 -->
      <div class="flex overflow-y-auto py-10px select-none">
        <div
          class="mr-5px px-10px py-6px rounded-xl cursor-pointer text-nowrap"
          v-for="(item, index) in categories"
          :key="index"
          :class="{ 'text-blue bg-blue-100 font-500': currentCategory === index }"
          @click="categoryClick(index, item.name)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="scrollable-content select-none">
      <div class="template-wrapper">
        <div class="template-item" v-for="(item, index) in templateList" :key="index">
          <ai-template-item v-bind="item" :type="item.templateType" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick, computed, watch } from 'vue'
import { getAiTemplateList, getAiTemplateListNoToken, getTemplateGroup } from '@/api/aiTemplate'
import aiTemplateItem from './aiTemplateItem.vue'
import { getToken } from '@/utils/token'
import { useRoute, useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'
import { useLogin } from '@/plugins/loginDialog'
const { showLogin } = useLogin()
// 分类数据
const categories = ref<any>([])
// const route = useRoute()
// const router = useRouter()
const currentCategory = ref(0)

const templateList = ref<any>([])

const pageParams = reactive({
  current: 0,
  pageSize: 1000,
  templateGroup: '热门推荐',
  keyword: ''
})

const handleSearch = debounce(() => {
  init()
}, 500)

async function init() {
  console.log('pageParams', pageParams.templateGroup)

  try {
    let res: any = null
    const token = getToken()
    if (token) {
      if (pageParams.templateGroup === '热门推荐') {
        console.log('进来了')

        pageParams.templateGroup = ''
        res = await getAiTemplateListNoToken(pageParams)
      } else if (pageParams.templateGroup === '我常用的') {
        pageParams.templateGroup = ''
        res = await getAiTemplateList(pageParams)
      } else {
        res = await getAiTemplateList(pageParams)
      }
    } else {
      if (pageParams.templateGroup === '热门推荐') {
        pageParams.templateGroup = ''
        res = await getAiTemplateListNoToken(pageParams)
      } else if (pageParams.templateGroup === '我常用的') {
        showLogin()
      } else {
        res = await getAiTemplateListNoToken(pageParams)
      }
    }
    templateList.value = res.data.records
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}
// 获取分组
async function getGroup() {
  const groupRes = await getTemplateGroup()
  categories.value.unshift({ name: '我常用的' })
  categories.value.unshift({ name: '热门推荐' })
  groupRes.data.map((item: any) => {
    categories.value.push({ name: item })
  })
}

const categoryClick = (id: any, item: string) => {
  currentCategory.value = id
  pageParams.templateGroup = item
  init()
}

getGroup()
init()
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.fixed-header {
  flex-shrink: 0;
}

.header {
  padding: 20px 0;

  .title-section {
    .search-box {
      max-width: 320px;
      :deep(.el-input__wrapper) {
        @apply bg-white shadow-none h-40px rounded-full;
      }
    }
  }
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.template-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.template-item {
  width: 100%;
  padding: 0;
}

.footer-wrapper {
  position: sticky;
  bottom: 0;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.footer {
  padding: 40px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
