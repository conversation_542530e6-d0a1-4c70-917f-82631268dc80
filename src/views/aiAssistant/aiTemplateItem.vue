<template>
  <div class="template-container" @click="openAiTemplatePage">
    <img
      class="template-logo"
      :src="templateImageUrl"
      v-if="templateImageUrl && templateImageUrl.includes('svg')"
    />
    <img class="template-logo" src="@/assets/static/icon/defaultTemplateIcon.svg" v-else />
    <span class="template-name text-ellipsis">{{ name }}</span>
  </div>
</template>

<script setup>
import { useStorage } from '@vueuse/core'
import { useRouter } from 'vue-router'
import { useLogin } from '@/plugins/loginDialog'
import { hasToken } from '@/utils/token'
const { showLogin } = useLogin()
const router = useRouter()

const props = defineProps({
  templateImageUrl: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  id: {
    type: [String, Number],
    default: null
  },
  type: {
    type: String,
    default: ''
  }
})

const openAiTemplatePage = () => {
  if (!hasToken()) {
    showLogin()
    return
  }

  // 深度思考类型
  if (props.type === 'directDialogue') {
    router.push({
      path: '/chat',
      query: {
        templateId: props.id,
        type: 'directDialogue',
        title: props.name
      }
    })
  } else {
    router.push({
      path: '/ai-template',
      query: {
        id: props.id,
        title: props.name
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.template-container {
  background: #ffffff;
  border-radius: 16px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: 90px;
  box-sizing: border-box;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  }

  .template-logo {
    width: 48px;
    height: 48px;
    margin-right: 16px;
    flex-shrink: 0;
  }

  .template-name {
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    flex: 1;
    min-width: 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    word-break: break-all;
  }
}
</style>
