<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Transformer } from 'markmap-lib'
import { Markmap } from 'markmap-view'
import * as htmlToImage from 'html-to-image'
import { saveAs } from 'file-saver'
import wx from 'weixin-js-sdk'

import { initialChat } from '@/api/aiTemplate'
import { wsClient } from '@/utils/websocket'
import { getCurrentChatRecord } from '@/api/aiTemplate'
import axios from 'axios'
const transformer = new Transformer()
const msgValue = ref('')

const isShowInput = ref(true)
const title = ref('')
const token = ref('')
const route = useRoute()
const queryConversationId = ref(route.query.conversationId)
isShowInput.value = route.query.type !== 'ylf'
title.value = route.query.title
token.value = route.query.token
console.log(route.query)
if (route.query.id) {
  getActivityList(token.value, route.query.id)
  // 设置网站标题
  document.title = `思维导图`
}

if (queryConversationId.value) {
  getCurrentChatRecord(queryConversationId.value).then((res) => {
    if (res.code === 20000) {
      markdownContent.value = res.data.records[0].message
    }
  })
}

// markdown文本内容 (web端)
const markdownContent = ref(
  `# 如何系统学习小学数学🚀(示例)
## 基础知识 😄
- 数字运算
- 整数
- 小数
- 分数
- 几何图形
- 平面图形
- 立体图形
## 学习方法 😣
- 课堂学习
- 认真听讲
- 积极提问
- 课后复习
- 完成作业
- 总结归纳
- 练习巩固
- 做练习题
- 模拟考试
## 学习资源 📚
- 教材课本
- 辅导资料
- 在线课程
## 兴趣培养 🎮
- 数学游戏
- 数学故事
- 实际应用
`
)
// markdown文本内容 (幼立方)
const markdownContentYlf = ref('')
const mm = ref()
const svgRef = ref()

const update = () => {
  let text = isShowInput.value ? markdownContent.value : markdownContentYlf.value
  // 处理文本换行
  text = processMarkdownForWrapping(text)
  const { root } = transformer.transform(text)
  mm.value.setData(root)
  setTimeout(() => {
    mm.value.fit()
  }, 1500)
}

// 监听markdown内容变化
// watch(
//   markdownContent,
//   () => {
//     update()
//   },
//   { deep: true }
// )

const zoomIn = () => mm.value.rescale(1.25)
const zoomOut = () => mm.value.rescale(0.8)
const fitToScreen = () => mm.value.fit()

// 判断是否在微信小程序环境
const isWechat = () => {
  return /MicroMessenger/i.test(navigator.userAgent)
}

const onSave = async () => {
  const dataUrl = await htmlToImage.toPng(svgRef.value, {
    quality: 1.0,
    pixelRatio: 4,
    backgroundColor: '#FFFFFF'
  })
  if (isWechat()) {
    // 直接执行小程序相关操作，不再添加事件监听器
    wx.miniProgram.navigateBack({ delta: 1 })
    wx.miniProgram.postMessage({ data: JSON.stringify(dataUrl) })
  } else {
    saveAs(dataUrl, 'markmap.png')
  }
}

const conversationId = ref('')
const isStreaming = ref(false)
const isRegenerating = ref(false)
const lastQuestionId = ref('')

// 文本自动换行处理函数
const wrapText = (text, maxLength = 25) => {
  if (text.length <= maxLength) return text

  // 移除已有的HTML标签
  const cleanText = text.replace(/<br\s*\/?>/gi, '')

  // 定义标点符号
  const punctuation = /[,，。！？；：、]/
  const lines = []
  let currentLine = ''

  for (let i = 0; i < cleanText.length; i++) {
    const char = cleanText[i]
    const nextChar = cleanText[i + 1]

    currentLine += char

    // 检查是否需要换行
    if (currentLine.length >= maxLength) {
      // 如果下一个字符是标点符号，先加上标点再换行，避免标点单独成行
      if (nextChar && punctuation.test(nextChar)) {
        currentLine += nextChar
        i++ // 跳过下一个字符，因为已经处理了
      }

      // 尝试在合适的位置断行（向前查找空格或标点）
      let breakIndex = -1
      const searchStart = Math.min(currentLine.length - 1, maxLength + 2) // 允许稍微超出一点来包含标点
      const searchEnd = Math.max(0, maxLength - 8)

      for (let j = searchStart; j >= searchEnd; j--) {
        if (/[\s,，。！？；：、]/.test(currentLine[j])) {
          breakIndex = j
          break
        }
      }

      if (breakIndex > 0 && breakIndex < currentLine.length - 1) {
        // 在找到的位置断行
        lines.push(currentLine.substring(0, breakIndex + 1).trim())
        currentLine = currentLine.substring(breakIndex + 1).trim()
      } else {
        // 没找到合适断点，检查是否以标点结尾
        if (punctuation.test(currentLine[currentLine.length - 1])) {
          // 如果以标点结尾，直接断行
          lines.push(currentLine.trim())
          currentLine = ''
        } else {
          // 强制断行
          lines.push(currentLine.trim())
          currentLine = ''
        }
      }
    }
  }

  if (currentLine.trim()) {
    lines.push(currentLine.trim())
  }

  return lines.join('<br/>')
}

// 处理 markdown 内容中的长文本
const processMarkdownForWrapping = (text) => {
  const lines = text.split('\n')
  const result = []

  for (let line of lines) {
    if (!line.trim()) {
      result.push(line)
      continue
    }

    // 处理标题行
    if (line.match(/^#+\s/)) {
      const titleMatch = line.match(/^(#+\s)(.+)/)
      if (titleMatch) {
        const prefix = titleMatch[1]
        const content = titleMatch[2]
        result.push(prefix + wrapText(content))
      } else {
        result.push(line)
      }
    }
    // 处理列表项
    else if (line.match(/^\s*-\s/)) {
      const listMatch = line.match(/^(\s*-\s)(.+)/)
      if (listMatch) {
        const prefix = listMatch[1]
        const content = listMatch[2]
        result.push(prefix + wrapText(content))
      } else {
        result.push(line)
      }
    }
    // 处理普通文本
    else {
      result.push(wrapText(line))
    }
  }

  return result.join('\n')
}

// 转换 markdown 格式的方法
const convertMarkdown = (text) => {
  const lines = text.split('\n')
  const result = []

  for (let line of lines) {
    if (!line.trim()) continue // 跳过空行

    // 计算开头的空格数量
    const spaceCount = line.match(/^\s*/)[0].length
    const content = line.trim()

    // 根据空格数量确定标题层级
    if (content.startsWith('- ')) {
      if (spaceCount === 0) {
        // 顶级标题
        result.push('# ' + content.substring(2))
      } else if (spaceCount === 2) {
        // 二级标题
        result.push('## ' + content.substring(2))
      } else {
        // 保持原有的列表项格式
        result.push(content)
      }
    } else {
      // 非列表项内容保持原样
      result.push(line)
    }
  }

  return result.join('\n')
}

//  WebSocket 消息处理函数
const handleWsMessage = async (event) => {
  const data = event.detail
  try {
    if (data.key === 'chat' && data.conversationId === conversationId.value) {
      if (data.error) {
        isStreaming.value = false
        isRegenerating.value = false
        return
      }
      // 如果是结束消息，保存 questionId
      if (data.end) {
        lastQuestionId.value = data.questionId
        isStreaming.value = false
        // 在结束时转换格式
        // markdownContent.value = convertMarkdown(markdownContent.value)
        return
      }
      // 处理普通聊天消息
      if (isStreaming.value) {
        if (data.value !== '```' && data.value !== 'markdown') {
          markdownContent.value += data.value
        }
      }
    }
    if (data.key === 'error') {
      isStreaming.value = false
    }
  } catch (error) {
    console.error('处理 WebSocket 消息失败:', error)
    isRegenerating.value = false
  }
}

// 发送消息的逻辑
const sendMessage = async () => {
  if (isStreaming.value) {
    return
  }
  markdownContent.value = ''
  let userMessage = msgValue.value?.trim()
  try {
    isStreaming.value = true
    // 确保有会话 ID
    if (!conversationId.value) {
      let initRes = null
      initRes = await initialChat({
        templateId: '2',
        message: userMessage
      })
      const id = initRes?.data?.conversationId || initRes?.conversationId
      if (id) {
        conversationId.value = id
      } else {
        throw new Error('获取会话ID失败')
      }
    }

    // 发送消息
    wsClient.send({
      key: 'chat',
      value: JSON.stringify({
        conversationId: conversationId.value,
        message: userMessage
      })
    })
  } catch (error) {
    console.error('发送消息失败:', error)
    isStreaming.value = false
  }
}

// 获取活动列表 单独请求
function getActivityList(token, subjectId) {
  // 开发 https://aihd.mypacelab.com  生产 https://aih.mypacelab.com
  let baseUrl = ''
  if (process.env.NODE_ENV === 'development') {
    baseUrl = 'https://aihd.mypacelab.com'
  } else {
    baseUrl = 'https://ai.mypacelab.com'
  }
  axios
    .get(`${baseUrl}/api/business/subject/stagePageList?subjectId=${subjectId}`, {
      headers: {
        Authorization: token
      }
    })
    .then((res) => {
      // 思维导图标题是课程名称，第一层子标题是阶段名称，第二层子标题是活动名称（形式）
      // 处理 res.data.data 结构
      if (res.data && res.data.data) {
        let markdownText = `# ${title.value}\n`

        // 处理每个阶段数据
        res.data.data.forEach((stage) => {
          markdownText += `## ${stage.title}\n`

          // 处理每个阶段下的活动
          if (stage.actList && stage.actList.length > 0) {
            stage.actList.forEach((activity) => {
              markdownText += `### ${activity.name}\n`
            })
          }
        })

        markdownContentYlf.value = markdownText
      } else {
        markdownContentYlf.value = `# ${title.value}`
      }

      // 更新思维导图
      update()
    })
}

onMounted(async () => {
  // 初始化markmap思维导图
  mm.value = Markmap.create(svgRef.value, {
    fit: true,
    style: (id) => `
      ${id} .markmap-foreign {
        font-size: 16px;
        line-height: 1.5;
      }
    `
  })
  // 更新思维导图渲染
  update()
  setTimeout(() => {
    fitToScreen()
  }, 300)

  const userInfo = JSON.parse(localStorage.getItem('USER_INFO') || '{}')
  if (userInfo?.id) {
    try {
      await wsClient.connect(userInfo.id)
      console.log('WebSocket 连接已建立')
    } catch (error) {
      console.error('WebSocket 连接失败:', error)
    }
  }
  window.addEventListener('ws-message', handleWsMessage)
})
</script>

<template>
  <div class="md:flex md:justify-between py-20px w-full h-100vh md:overflow-hidden overflow-y-auto">
    <div
      v-if="isShowInput"
      class="w-full md:w-360px md:h-full mr-10px flex justify-between flex-col"
    >
      <div class="bg-white rounded-md p-10px mb-10px">
        <div class="text-16px font-bold mb-10px">需求描述<span>*</span></div>
        <textarea
          v-model="msgValue"
          class="bg-slate-100 border-none w-full h-130px md:h-160px p-10px text-16px resize-none outline-blue outline-2 outline-offset-2 rounded-md"
          placeholder="简单描述"
        ></textarea>
      </div>
      <div
        @click="sendMessage"
        class="text-center text-16px font-bold bg-blue-600 text-white rounded-md p-10px cursor-pointer active:bg-blue-500 select-none mb-10px md:mb-0"
      >
        AI生成思维导图文本
      </div>
      <div bg-white rounded-md p-10px mb-10px>
        <div class="text-16px font-bold mb-10px flex justify-between items-center">
          <span>调整修改</span>
          <el-popover
            placement="bottom-start"
            trigger="hover"
            popper-class="popover-class"
            :show-arrow="true"
          >
            <template #reference>
              <div class="text-12px text-red-400 cursor-pointer">格式说明</div>
            </template>
            <div class="p-10px lh-30px">
              <p># 表示一级标题</p>
              <p>## 表示二级标题</p>
              <p>### 表示三级标题</p>
              <p>- 表示列表项</p>
            </div>
          </el-popover>
        </div>
        <textarea
          v-model="markdownContent"
          class="bg-slate-100 border-none w-full h-230px md:h-400px p-10px text-16px resize-none outline-blue outline-2 outline-offset-2 rounded-md"
          placeholder="请输入Markdown文本..."
        ></textarea>
      </div>
      <div
        @click="update"
        class="text-center text-16px font-bold bg-orange-600 text-white rounded-md p-10px cursor-pointer active:bg-orange-500 select-none mb-10px md:mb-0"
      >
        生成思维导图
      </div>
    </div>

    <!-- 思维导图展示区 -->
    <div class="mindmap-viewer w-full h-600px md:h-full flex flex-col min-h-0">
      <div class="svg-container">
        <svg ref="svgRef" width="100%" height="100%" style="display: block"></svg>
      </div>
      <div class="controls">
        <el-button @click="zoomIn">放大</el-button>
        <el-button @click="zoomOut">缩小</el-button>
        <el-button @click="fitToScreen">适应屏幕</el-button>
        <el-button id="download" type="primary" @click="onSave">下载</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mindmap-viewer {
  .svg-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #fff;

    svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
  .controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background-color: #fff;
    border-top: 1px solid #eee;
  }
}
</style>
<style>
.markmap-foreign {
  font-size: 16px;
  line-height: 1.5;
}
</style>
