<template>
  <div max-w-950px mx-auto py-30px>
    <div h-150px bg-indigo-100 rounded-md flex items-center justify-between px-20px mb-20px>
      <div cursor-pointer text-center>
        <p text-3xl mb-10px v-if="!isLogin">未登录</p>
        <p text-3xl mb-10px v-else>{{ userInfo.nickName }}</p>
      </div>
      <div v-if="!isLogin">
        <el-button w-120px type="primary" @click="handleLogin">登录</el-button>
      </div>
    </div>
    <div class="app-container">
      <div @click="openNicknameDialog">修改昵称</div>
      <div @click="contactCustomerService">联系客服</div>
      <div @click="showDialog('yh')">平台服务协议</div>
      <div @click="showDialog('ys')">隐私政策</div>
      <div v-if="isLogin" @click="handleLogout">注销登录</div>
    </div>
  </div>

  <!-- 修改昵称弹窗 -->
  <el-dialog v-model="nicknameDialogVisible" title="修改昵称" width="30%">
    <el-form :model="nicknameForm" label-width="80px">
      <el-form-item label="昵称">
        <el-input v-model="nicknameForm.nickName" placeholder="请输入新昵称"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="nicknameDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitNickname" :loading="submitting">确认</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 隐私政策弹窗 -->
  <privacyPolicyDialog ref="privacyPolicyRef"></privacyPolicyDialog>
</template>

<script setup lang="ts">
import { useLogin } from '@/plugins/loginDialog'
import { ref, onMounted, onUnmounted } from 'vue'
import { hasToken, removeToken } from '@/utils/token'
import emitter from '@/utils/eventBus'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { updateUser, checkToken } from '@/api/login'
import privacyPolicyDialog from './privacyPolicyDialog.vue'
const { showLogin } = useLogin()
const userInfo = ref<any>({})
const isLogin = ref(false)
const router = useRouter()
const privacyPolicyRef = ref<any>(null)
// 修改昵称相关变量
const nicknameDialogVisible = ref(false)
const nicknameForm = ref({
  nickName: ''
})
const submitting = ref(false)

// 联系客服
const contactCustomerService = () => {
  const externalLink = 'https://work.weixin.qq.com/kfid/kfc33433ad794129a42'
  window.open(externalLink)
}

const showDialog = (type: any) => {
  privacyPolicyRef.value.show(type)
}

// 打开修改昵称弹窗
const openNicknameDialog = () => {
  if (!isLogin.value) {
    ElMessage.warning('请先登录')
    return
  }
  nicknameForm.value.nickName = userInfo.value.nickName || ''
  nicknameDialogVisible.value = true
}

// 提交修改昵称
const submitNickname = async () => {
  if (!nicknameForm.value.nickName.trim()) {
    ElMessage.warning('昵称不能为空')
    return
  }

  submitting.value = true
  try {
    await updateUser({
      nickName: nicknameForm.value.nickName,
      id: userInfo.value.id
    })

    // 使用 checkToken 接口刷新用户信息
    const res = await checkToken()
    if (res.code === 20000 && res.data) {
      userInfo.value = res.data.userInfo
      localStorage.setItem('USER_INFO', JSON.stringify(res.data.userInfo))
    } else {
      // 如果接口失败，至少更新本地昵称
      userInfo.value.nickName = nicknameForm.value.nickName
      localStorage.setItem('USER_INFO', JSON.stringify(userInfo.value))
    }

    ElMessage.success('昵称修改成功')
    nicknameDialogVisible.value = false
  } catch (error) {
    console.error('修改昵称失败:', error)
    ElMessage.error('修改昵称失败，请重试')
  } finally {
    submitting.value = false
  }
}

const handleLogin = () => {
  showLogin({
    onSuccess: () => {
      isLogin.value = true
      userInfo.value = JSON.parse(localStorage.getItem('USER_INFO') || '{}')
      emitter.emit('login')
    },
    onCancel: () => {}
  })
}

if (hasToken()) {
  console.log('有 token', hasToken())
  isLogin.value = true
  userInfo.value = JSON.parse(localStorage.getItem('USER_INFO') || '{}')
} else {
  isLogin.value = false
}

const handleLogout = () => {
  localStorage.removeItem('USER_INFO')
  removeToken()
  isLogin.value = false
  emitter.emit('logout')
  ElMessage.success('退出成功')
  router.push('/')
}

onMounted(() => {
  emitter.on('logout', () => {
    isLogin.value = false
    userInfo.value = {}
  })
  emitter.on('login', () => {
    isLogin.value = true
    userInfo.value = JSON.parse(localStorage.getItem('USER_INFO') || '{}')
  })
})

onUnmounted(() => {
  emitter.off('logout')
  emitter.off('login')
})
</script>

<style scoped>
.app-container {
  @apply grid grid-cols-3 gap-20px;
  & > div {
    @apply bg-white rounded-md p-20px cursor-pointer;
  }
}
</style>
