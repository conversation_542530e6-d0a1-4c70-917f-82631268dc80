<template>
  <div
    @click="onTap"
    class="container"
    :class="{
      'text-button': type === 'text',
      default: type === 'default',
      'secondary-text': type === 'secondary',
    }"
  >
    <el-icon :component="getIconComponent(icon)" />
    <span>{{ text }}</span>
  </div>
</template>
<script setup>
import { defineProps } from 'vue'
import * as ElementPlusIcons from '@element-plus/icons-vue'

const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  onTap: {
    type: Function,
    default: () => {},
  },
  size: {
    type: Number,
    default: 14,
  },
  color: {
    type: String,
    default: "#333333",
  },
  text: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    default: "default",
  },
  customIcon: {
    type: Boolean,
    default: true,
  },
})

const getIconComponent = (icon) => {
  return ElementPlusIcons[icon] || null
}
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.default {
  display: flex;
  height: 40px;
  border-radius: 30px;
  border: 1px solid #fff;
  padding: 10px 20px;
  background-color: #f7fbff;
  font-size: 14px;
}

.text-button {
  display: inline-flex;
  color: #808080;
  font-size: 14px;
}

.secondary-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #808080;
  white-space: nowrap;
}
</style>
