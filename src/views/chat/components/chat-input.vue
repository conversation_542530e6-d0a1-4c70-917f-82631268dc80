<template>
  <div class="input-area">
    <div class="icon-button-wrapper">
      <div @click="openNewChat">
        <div class="i-uiw-message text-sm"></div>
        新建会话
      </div>
      <div @click="exportWordAll">
        <div class="i-uiw-upload text-sm"></div>
        导出完整对话
      </div>
      <div @click="gotChatRecord">
        <div class="i-uiw-time-o text-sm"></div>
        历史记录
      </div>
    </div>
    <div class="text-area-wrapper">
      <textarea
        v-model="inputValue"
        @keydown.enter.prevent="handleEnter"
        placeholder="有问题，就告诉我吧～"
        class="resize-none outline-none border-none h-80px w-full text-16px"
      />
      <div
        :class="isSendDisabled ? 'bg-gray-50 rounded-full p-8px' : 'bg-gray-800 rounded-full p-8px'"
      >
        <div
          @click="sendMessage"
          class="i-uiw-arrow-up text-20px"
          :class="isSendDisabled ? 'text-slate-500' : 'text-white cursor-pointer'"
        ></div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElInput, ElButton, ElIcon } from 'element-plus'
import IconButton from './icon-button.vue'
import { ArrowUp } from '@element-plus/icons-vue'

const router = useRouter()

const props = defineProps({
  isStreaming: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: String,
    default: ''
  },
  sendMessage: {
    type: Function
  },
  exportWordAll: {
    type: Function
  },
  keyboardHeight: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:modelValue'])

const inputValue = ref('')

watch(
  () => props.modelValue,
  (newVal) => {
    inputValue.value = newVal
  },
  { immediate: true }
)

watch(
  () => inputValue.value,
  (newVal) => {
    emit('update:modelValue', newVal)
  }
)

const isSendDisabled = computed(() => {
  return !props.modelValue.trim() || props.isStreaming
})

const openNewChat = () => {
  // 这里需要根据 Vue Router 的使用情况进行调整
  router.push('/chat/new')
}

const getInputAreaHeight = () => {
  return new Promise((resolve) => {
    const height = document.querySelector('.input-area').offsetHeight
    resolve(height || 0)
  })
}

// 跳转历史记录
const gotChatRecord = () => {
  router.push('/chatRecord')
}

// 处理回车键
const handleEnter = (e) => {
  if (e.shiftKey) {
    // SHIFT + ENTER: 插入换行
    const textarea = e.target
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    inputValue.value = inputValue.value.substring(0, start) + '\n' + inputValue.value.substring(end)
    // 设置光标位置到换行后
    nextTick(() => {
      textarea.selectionStart = textarea.selectionEnd = start + 1
    })
  } else {
    // 仅按 ENTER: 发送消息
    props.sendMessage?.()
  }
}

defineExpose({
  isSendDisabled,
  getInputAreaHeight
})
</script>
<style lang="scss" scoped>
.input-area {
  position: sticky;
  bottom: 0;
  z-index: 99;
  padding: 10px 0px 30px 0px;
  will-change: transform;
  @apply bg-slate-100;
  .icon-button-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    & > div {
      padding: 10px 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 5px;
      border: 1px solid #e5e7eb;
      border-radius: 24px;
      @apply text-14px font-500;
    }
  }
  .text-area-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: 16px;
    flex: 1;
    background: #ffffff;
  }
}
.plus-icon {
  color: #666;
  margin-right: 16px;
}
.send-icon {
  background-color: #367cff;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &.disabled {
    background-color: #ccc;
    opacity: 0.6;
    pointer-events: none;
  }
}
</style>
