/**
 * 从 URL 查询参数中解析指定的键值
 * @param keys - 要解析的键名数组
 * @returns 解析后的值数组
 */
export const parseParams = (...keys: string[]): (string | undefined)[] => {
  const query = new URLSearchParams(window.location.search)
  return keys.map((key) => query.get(key) || undefined)
}

/**
 * 格式化日期字符串或时间戳为时间戳
 * @param date - 日期字符串或时间戳
 * @returns 时间戳
 */
export const formatDate = (date: string | number): number => {
  if (typeof date === 'string') {
    // 处理日期字符串中的空格，确保 Safari 兼容性
    const formattedDate = date.replace(/\s/, 'T')
    return new Date(formattedDate).getTime()
  }
  return date
}

/**
 * 格式化文件大小
 * @param size - 文件大小（字节）
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (size: number): string => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(2)}${units[index]}`
}
