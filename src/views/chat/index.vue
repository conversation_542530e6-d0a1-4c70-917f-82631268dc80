<template>
  <div class="chat-page max-w-1000px mx-auto">
    <div py-5 text-center text-18px>{{ chatTitle }}</div>
    <div class="message-item-wrap">
      <div
        v-for="(item, index) in messageList"
        :key="index"
        :id="'msg-' + index"
        class="message-item"
        :class="{
          'message-user': item.type === 'user',
          'message-ai': item.type === 'ai',
          'last-message': index === messageList.length - 1
        }"
      >
        <div class="message-content">
          <div
            v-if="item.type === 'ai'"
            class="ai-message"
            :class="{ 'error-message': item.isError }"
          >
            <div
              v-if="item.reasonContent"
              class="flex items-center py-10px px-10px w-130px cursor-pointer bg-gray-100 mb-10px rounded-md"
              @click="showReason(index)"
            >
              <span>已深度思考</span>
              <div v-if="!reasonVisible[index]" class="i-uiw-down ml-5px"></div>
              <div v-else class="i-uiw-up ml-5px"></div>
            </div>
            <MdPreview
              v-if="item.reasonContent"
              v-show="!reasonVisible[index]"
              id="preview-only1"
              :modelValue="item.reasonContent"
              previewTheme="reasonTheme"
            />
            <MdPreview id="preview-only2" :modelValue="item.content" />
            <div
              v-if="item.type === 'ai' && !isStreaming && !isWelcomeMessage(item, index) && !item.isError"
              class="action-btn-wrapper mt-10px"
              :id="'msg-' + index"
            >
              <div v-if="item.hasMultipleAnswers" class="answer-navigation">
                <span class="text-14px text-gray-500"
                  >{{ item.currentAnswerIndex + 1 }}/{{ item.answers.length }}</span
                >
                <div v-if="item.currentAnswerIndex > 0" @click="switchAnswer(item, -1)">上一个</div>
                <div
                  v-if="item.currentAnswerIndex < item.answers.length - 1"
                  @click="switchAnswer(item, 1)"
                >
                  下一个
                </div>
              </div>
              <div
                cursor-pointer
                active:bg-gray-100
                px-3px
                py-2px
                v-if="index === messageList.length - 1"
                @click="handleRegenerate(item.id)"
              >
                <div class="i-uiw:reload text-12px" style="vertical-align: -0.125em"></div>
                重来
              </div>
              <div class="action-btn-group">
                <div
                  cursor-pointer
                  active:bg-gray-100
                  px-3px
                  py-2px
                  text-14px
                  text-slate-500
                  @click="handleFeedback(item.id)"
                >
                  <div class="i-uiw:dislike-o text-12px" style="vertical-align: -0.125em"></div>
                  不好
                </div>
                <div
                  cursor-pointer
                  active:bg-gray-100
                  px-3px
                  py-2px
                  text-14px
                  text-slate-500
                  @click="copyMessage(item.content)"
                >
                  <div class="i-uiw:copy text-12px" style="vertical-align: -0.125em"></div>
                  复制
                </div>
                <div
                  cursor-pointer
                  active:bg-gray-100
                  px-3px
                  py-2px
                  text-14px
                  text-slate-500
                  @click="exportWord(item.id)"
                >
                  <div class="i-uiw:upload text-12px" style="vertical-align: -0.125em"></div>
                  导出
                </div>
              </div>
            </div>
          </div>

          <text v-else class="message-text" :user-select="true">{{ item.content }}</text>
        </div>
      </div>

      <!-- Loading message -->
      <div v-if="isStreaming" class="message-item message-ai">
        <div class="message-content">
          <div class="ai-message">
            <div class="thinking-message">正在思考中</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error retry message -->
    <div id="retry-message" class="retry-container" v-if="hasError" @tap="handleRetry">
      <span class="retry-text">{{ errorMessage }}，请重试</span>
    </div>

    <chat-input
      v-model="inputMessage"
      :sendMessage="sendMessage"
      :exportWordAll="exportWordAll"
      :isStreaming="isStreaming"
      ref="chatInputRef"
    />
  </div>
</template>

<script setup lang="ts">
import { useTemplateStore } from '@/stores/template'
import { ref, nextTick, onMounted, watch, onUnmounted, getCurrentInstance } from 'vue'
import { useRoute } from 'vue-router'
import {
  getCurrentChatRecord,
  createByAiTemplate,
  submitFeedback,
  initialChat,
  regenerateAnswer,
  generateWord,
  generateWholeWord
} from '@/api/aiTemplate'
import ChatInput from './components/chat-input.vue'
import { debounce } from 'lodash-es'
import { wsClient } from '@/utils/websocket'
import type { MessageItem } from '@/types/chat'
import { showToast, showLoading, setClipboardData, downloadFile, storage } from '@/utils/webUtils'
import type { ComponentInternalInstance } from 'vue'
import type { ChatRecord, ChatInputRef, ToastRef } from '@/types/api'
import { MdPreview } from 'md-editor-v3'
import 'md-editor-v3/lib/preview.css'
const route = useRoute()
const reasonVisible = ref<Record<number, boolean>>({})
// 显示/隐藏理由的方法
const showReason = (index: number) => {
  reasonVisible.value[index] = !reasonVisible.value[index]
}
// 聊天相关状态
const chatInfo = ref<any>({
  conversationId: '',
  type: ''
})
const messageList = ref<any[]>([])
const inputMessage = ref('')
const isStreaming = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const errorType = ref('') // 保留 errorType 用于区分重试逻辑
// 滚动位置控制
const currentScrollTarget = ref<string>('') // 改名为 currentScrollTarget

const chatInputRef = ref<ChatInputRef | null>(null)

// 添加 conversationId 的响应式引用
const conversationId = ref<any>(null)

// 添加状态来保存最后一条消息的 questionId
const lastQuestionId = ref<any>(null)

const isRegenerating = ref(false)

const templateStore = useTemplateStore()

// 页面传递的数据
const pageData = ref<any>(null)

// 页面 title
const chatTitle = ref<any>('AI助手')

// 定义滚动函数的类型
interface ScrollFunction extends Function {
  isRunning?: boolean
}

// 滚动到指定消息
const scrollToMessageById = (messageId: string) => {
  // 改名为 scrollToMessageById
  const element = document.getElementById(messageId)
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })
  }
}

// 滚动到最后一条消息
const scrollToLastMessage: ScrollFunction = async () => {
  if (scrollToLastMessage.isRunning) return
  scrollToLastMessage.isRunning = true

  try {
    await nextTick()
    const messageContainer = document.querySelector('.message-item-wrap')
    const lastMessage = messageContainer?.querySelector('.message-item:last-child')

    if (lastMessage) {
      lastMessage.scrollIntoView({
        behavior: 'smooth',
        block: 'end'
      })
    } else {
      messageContainer?.scrollTo({
        top: messageContainer.scrollHeight,
        behavior: 'smooth'
      })
    }
  } catch (error) {
    console.error('滚动失败:', error)
  } finally {
    setTimeout(() => {
      scrollToLastMessage.isRunning = false
    }, 100)
  }
}

// 滚动到页面底部
const scrollToPageBottom = () => {
  nextTick(() => {
    const mainEl = document.querySelector('.main')
    if (mainEl) {
      mainEl.scrollTo({
        top: mainEl.scrollHeight,
        behavior: 'smooth'
      })
    }
  })
}
// 组合滚动：先滚动到消息，然后滚动到页面底部
const scrollToMessageAndBottom = async (messageId: string) => {
  // 先滚动到指定消息
  // scrollToMessageById(messageId)
  // 等待一段时间后滚动到页面底部
  // setTimeout(() => {
  //   scrollToPageBottom()
  // }, 50)
}

// 监听消息列表变化，自动滚动到底部
watch(
  () => messageList.value.length,
  async () => {
    await nextTick()
    // scrollToLastMessage()
    scrollToPageBottom()
  }
)

// 修改页面初始化逻辑
const onPageInit = async () => {
  console.log('页面初始化', pageData.value)
  try {
    const { id, type, templateId, templateValue, title } = pageData.value || {}
    // 设置页面标题
    document.title = title || 'AI助手'

    if (type === 'template' && templateId) {
      // templateValue 已经是对象数组，不需要解析
      const templateData = templateValue // 直接使用 templateValue

      // 调用模板生成API
      const res = await createByAiTemplate({
        templateId: templateId,
        templateValue: JSON.stringify(templateData) // 这里需要转换为字符串
      })

      if (res.code === 20000 && res.data) {
        const { conversationId: chatId, message } = res.data
        if (!chatId) {
          throw new Error('获取会话ID失败')
        }
        wsClient.send({
          key: 'start',
          value: JSON.stringify(res.data)
        })

        // 设置会话ID
        conversationId.value = chatId
        chatInfo.value = {
          conversationId: chatId,
          type: 'template',
          templateId
        }
        // 格式化模板返回的消息作为用户消息
        if (res.data.messageType === 'multiText') {
          try {
            const messages = JSON.parse(res.data.message)
            messages.forEach((msg: any) => {
              messageList.value.push({
                type: 'user',
                content: msg.text
              })
            })
          } catch (e) {
            console.error('解析多行文本失败:', e)
            messageList.value.push({
              type: 'user',
              content: res.data.message
            })
          }
        } else {
          messageList.value.push({
            type: 'user',
            content: res.data.message
          })
        }

        // 设置流式响应状态为 true（在发送消息之前）
        isStreaming.value = true
        // uni.$on('ws-message', handleWsMessage)
        // 调用一下聊天记录接口（不处理返回结果）
        getCurrentChatRecord(res.data.conversationId)
      } else {
        throw new Error(res.message || '创建模板对话失败')
      }
    } else if (type === 'directDialogue') {
      // 处理深度思考类型
      chatInfo.value = {
        type: 'directDialogue',
        templateId
      }
      messageList.value = [
        {
          type: 'ai',
          content: `老师您好，我是基于Deepseek R1的教研AI助手，无论是主题课程背景知识、课程设计、教学实施，还是日常工作的相关问题，我都会努力满足您的需求。请告诉我具体问题，让我们一起开启智能教学研之旅！`
        }
      ]
    } else if (type === 'chatRecord' && id) {
      try {
        conversationId.value = id
        const res = await getCurrentChatRecord(id)
        if (res.code === 20000) {
          if (res.data?.records?.length > 0) {
            messageList.value = formatChatRecords(res.data.records)
            // 设置会话信息
            chatInfo.value = {
              conversationId: id,
              type: 'chatRecord'
            }
            nextTick(() => {
              scrollToMessageAndBottom('msg-' + (messageList.value.length - 1))
              // scrollToMessageById('msg-' + (messageList.value.length - 1))
            })
          } else {
            messageList.value = [{ type: 'ai', content: '你好！我是青禾AI，有什么可以帮你的吗？' }]
          }
        } else {
          throw new Error(res.message || '获取聊天记录失败')
        }
      } catch (error) {
        console.error('获取聊天记录失败:', error)
        handleError(error, '获取聊天记录失败', 'record')
        messageList.value = [{ type: 'ai', content: '你好！我是青禾AI，有什么可以帮你的吗？' }]
      }
    } else {
      messageList.value = [{ type: 'ai', content: '你好！我是青禾AI，有什么可以帮你的吗？' }]
    }
  } catch (error) {
    console.error('页面初始化失败:', error)
  }
}

// 修改错误处理函数
const handleError = (error: unknown, message: string, type: string) => {
  hasError.value = true
  errorMessage.value = message
  errorType.value = type
  nextTick(() => {
    // 设置滚动目标
    currentScrollTarget.value = 'retry-message'
    scrollToMessageById('retry-message') // 直接调用滚动
  })
}

// 修改 WebSocket 消息处理函数
const handleWsMessage = async (event: any) => {
  const data = event.detail
  try {
    if (data.key === 'chat' && data.conversationId === conversationId.value) {
      if (data.error) {
        messageList.value.push({
          type: 'ai',
          content: data.errorMessage,
          isError: true
        })
        isStreaming.value = false
        isRegenerating.value = false
        return
      }
      // 如果是结束消息，保存 questionId
      if (data.end) {
        lastQuestionId.value = data.questionId
        isStreaming.value = false
        if (isRegenerating.value) {
          try {
            const recordRes = await getCurrentChatRecord(chatInfo.value.conversationId)
            if (recordRes.code === 20000 && recordRes.data?.records?.length > 0) {
              messageList.value = formatChatRecords(recordRes.data.records)
              // 滚动到最新消息
              nextTick(() => {
                scrollToMessageById('msg-' + (messageList.value.length - 1))
              })
            }
          } catch (error) {
            console.error('获取聊天记录失败:', error)
          } finally {
            isRegenerating.value = false
          }
          return
        }
        // 更新最后一条AI消息的id
        const lastMessage = messageList.value[messageList.value.length - 1]
        if (lastMessage && lastMessage.type === 'ai') {
          lastMessage.id = data.questionId
        }
        // 等待DOM更新完成后再滚动
        nextTick(() => {
          setTimeout(() => {
            // 确保所有消息都在滚动前被正确渲染
            const allMessages = [...messageList.value]
            messageList.value = allMessages
            // scrollToLastMessage()
            scrollToPageBottom()
          }, 500)
        })
        return
      }
      // 处理普通聊天消息
      const content = data.value || ''

      if (isStreaming.value && !isRegenerating.value) {
        // 区分是重新生成还是普通对话
        if (isRegenerating.value) {
          // 重新生成时,添加新的AI回复
          if (data.reason) {
            messageList.value.push({
              type: 'ai',
              reasonContent: data.reason ? content : ''
            })
          } else {
            messageList.value.push({
              type: 'ai',
              content: '',
              reasonContent: data.reason ? content : ''
            })
          }
        } else {
          // 普通对话时的逻辑保持不变
          isStreaming.value = false
          messageList.value.push({
            type: 'ai',
            content: '',
            reasonContent: data.reason ? content : ''
          })
        }
      } else {
        // 追加内容到最后一条消息
        const lastMessage = messageList.value[messageList.value.length - 1]
        if (lastMessage && lastMessage.type === 'ai') {
          if (data.reason) {
            // 如果是 reason 内容，追加到 reasonContent
            lastMessage.reasonContent = (lastMessage.reasonContent || '') + content
          } else {
            // 否则追加到普通 content
            lastMessage.content = (lastMessage.content || '') + content
          }
        } else {
          // 如果没有最后一条消息或不是AI消息，创建新消息
          messageList.value.push({
            type: 'ai',
            content: data.reason ? '' : content,
            reasonContent: data.reason ? content : ''
          })
        }
      }
    }
    if (data.key === 'error') {
      handleError(new Error(data.value || '请求失败'), '请求失败', 'send')
      isStreaming.value = false
    }
    // 如果key是warn 提示消息内容
    if (data.key === 'warn') {
      showToast({
        title: data.value,
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('处理 WebSocket 消息失败:', error)
    handleError(error, '消息处理失败', 'send')
    isRegenerating.value = false
  }
}

// 修改发送消息的逻辑
const sendMessage = async (isRetry = false) => {
  // uni.$on('ws-message', handleWsMessage)
  if (isStreaming.value) {
    return
  }
  // 重试时不校验发送按钮是否可用
  if (!isRetry && (!chatInputRef.value || chatInputRef.value?.isSendDisabled)) {
    console.log('发送按钮不可用')
    return
  }

  let userMessage = inputMessage.value?.trim()
  if (!userMessage && isRetry) {
    const lastUserMessage = [...messageList.value].reverse().find((item) => item.type === 'user')
    if (lastUserMessage) {
      userMessage = lastUserMessage.content
    }
  }

  if (!userMessage) {
    return
  }
  messageList.value.push({ type: 'user', content: userMessage })
  inputMessage.value = ''
  try {
    setTimeout(() => {
      // scrollToLastMessage()
      scrollToPageBottom()
    }, 100)

    isStreaming.value = true

    // 确保有会话 ID
    if (!conversationId.value) {
      let initRes: any = null
      if (chatInfo.value.type == 'directDialogue') {
        initRes = await initialChat({
          templateId: '1',
          message: userMessage
        })
      } else {
        initRes = await initialChat({
          data: {
            conversationId: '',
            message: userMessage
          }
        })
      }
      const id = initRes?.data?.conversationId || initRes?.conversationId
      if (id) {
        conversationId.value = id
        chatInfo.value.conversationId = id
      } else {
        throw new Error('获取会话ID失败')
      }
    }

    // 发送消息
    wsClient.send({
      key: 'chat',
      value: JSON.stringify({
        conversationId: conversationId.value,
        message: userMessage
      })
    })
  } catch (error) {
    console.error('发送消息失败:', error)
    isStreaming.value = false
    handleError(error, '发送失败，请重试', 'send')
  }
}

// 重试功能
const handleRetry = async () => {
  if (!chatInfo.value.conversationId) {
    showToast({
      title: '会话已失效，请重新开始',
      icon: 'none'
    })
    return
  }
  hasError.value = false
  try {
    switch (errorType.value) {
      case 'init':
        await sendMessage(true)
        break

      case 'record':
        // 重试获取聊天记录，传递正确的 conversationId
        const id = chatInfo.value.conversationId?.data || chatInfo.value.conversationId
        const recordRes = await getCurrentChatRecord(id)
        if (recordRes.code === 20000 && recordRes.data) {
          messageList.value = recordRes.data
            .map((item: any) => [
              { type: 'user', content: item.question || item.content },
              { type: 'ai', content: item.answer || item.contentRes }
            ])
            .flat()
        } else {
          throw new Error(recordRes.message || '获取聊天记录失败')
        }
        break

      case 'template':
        // 重试模板创建
        await onPageInit()
        break

      case 'send':
        // 重试发送消息
        await sendMessage(true)
        break
    }
  } catch (error) {
    console.error('重试失败:', error)
    handleError(error, `重试${errorType.value}失败`, errorType.value)
  }
}

// 修改复制功能
const copyMessage = async (content: string) => {
  await setClipboardData({
    data: content,
    success: () => {
      showToast({ title: '复制成功', icon: 'success' })
    },
    fail: () => {
      showToast({ title: '复制失败', icon: 'error' })
    }
  })
}

// 使用 debounce 创建防抖函数
const handleFeedback = debounce(
  async (questionId: any) => {
    try {
      await submitFeedback(questionId)
      showToast({
        title: '反馈成功',
        icon: 'success'
      })
    } catch (error) {
      showToast({
        title: '反馈失败',
        icon: 'error'
      })
    }
  },
  2000,
  { leading: true, trailing: false }
)

// 修改重来按钮的处理函数
const handleRegenerate = async (id: string | number) => {
  if (!id) {
    showToast({
      title: '无法重新生成，请重试',
      icon: 'error'
    })
    return
  }
  try {
    // 显示加载状态
    isStreaming.value = true
    // 设置重新生成状态
    isRegenerating.value = true
    // 删除最后一条 AI 回复
    const lastMessage = messageList.value[messageList.value.length - 1]
    if (lastMessage && lastMessage.type === 'ai') {
      messageList.value.pop()
    }
    // 调用重新生成接口
    const res = await regenerateAnswer(id)
    if (res.code !== 20000) {
      throw new Error(res.message || '重新生成失败')
    }
  } catch (error: any) {
    console.error('重新生成失败:', error)
    showToast({
      title: error.message || '重新生成失败',
      icon: 'error'
    })
    isRegenerating.value = false
    isStreaming.value = false
  }
}

// 添加判断是否是欢迎消息的函数
const isWelcomeMessage = (item: MessageItem, index: number): boolean => {
  return (
    index === 0 &&
    !!item.content &&
    (item.content === '你好！我是青禾AI，有什么可以帮你的吗？' ||
      item.content.includes('老师您好，我是基于Deepseek R1的教研AI助手'))
  )
}

// 修改聊天记录处理逻辑
const formatChatRecords = (records: ChatRecord[]): MessageItem[] => {
  // 添加日期格式化辅助函数
  const formatDate = (dateStr: any): number => {
    // 返回类型改为 number
    if (!dateStr) return Date.now()
    // 将日期字符串转换为时间戳
    return new Date(dateStr.replace(/\s/, 'T')).getTime()
  }

  const formattedMessages: MessageItem[] = []
  const answerGroups = new Map<string | number, any[]>() // 指定 Map 的类型
  let lastAiMessage: any = null // 使用 any 类型
  records.forEach((record) => {
    // 处理用户消息
    if (record.role === 'user') {
      // 处理多行文本
      if (record.messageType === 'multiText') {
        try {
          const messages = JSON.parse(record.message)
          messages.forEach((msg: any) => {
            formattedMessages.push({
              type: 'user',
              content: msg.text,
              id: record.id,
              timestamp: record.createTime
            })
          })
        } catch (e) {
          console.error('解析多行文本失败:', e)
          const content = record.message
          formattedMessages.push({
            type: 'user',
            content: content,
            id: record.id,
            timestamp: record.createTime
          })
        }
      } else {
        const content = record.message
        formattedMessages.push({
          type: 'user',
          content: content,
          id: record.id,
          timestamp: record.createTime
        })
      }
    }
    // 处理AI回复
    else if (record.role === 'assistant') {
      lastAiMessage = record // 记录最后一条AI消息
      // 如果有answerId，说明是对同一问题的回复
      if (record.answerId) {
        if (!answerGroups.has(record.answerId)) {
          answerGroups.set(record.answerId, [])
        }
        answerGroups.get(record.answerId)?.push({
          reasonContent: record.reason || record.reasonContent,
          content: record.message,
          timestamp: record.createTime,
          id: record.id
        })
      } else {
        // 普通回复
        formattedMessages.push({
          type: 'ai',
          reasonContent: record.reason || record.reasonContent,
          content: record.message,
          id: record.id,
          timestamp: record.createTime
        })
      }
    }
  })

  // 修改处理分组回复的逻辑
  answerGroups.forEach((answers: any[], answerId: string | number) => {
    if (answers.length > 1) {
      // 按时间排序（降序 - 最新的在前面）
      answers.sort((a: any, b: any) => formatDate(b.timestamp) - formatDate(a.timestamp))

      formattedMessages.push({
        type: 'ai',
        reasonContent: answers[0].reasonContent,
        content: answers[0].content,
        id: answers[0].id,
        timestamp: answers[0].timestamp,
        hasMultipleAnswers: true,
        answers,
        currentAnswerIndex: 0
      } as MessageItem) // 使用类型断言
    } else if (answers.length === 1) {
      formattedMessages.push({
        type: 'ai',
        reasonContent: answers[0].reasonContent,
        content: answers[0].content,
        id: answers[0].id,
        timestamp: answers[0].timestamp
      } as MessageItem) // 使用类型断言
    }
  })

  // 更新 lastQuestionId
  if (lastAiMessage) {
    lastQuestionId.value = lastAiMessage.id
  }

  // 修改最终排序逻辑
  return formattedMessages.sort(
    (a: any, b: any) => formatDate(a.timestamp) - formatDate(b.timestamp)
  )
}

// 添加切换回复的方法
const switchAnswer = (item: MessageItem, direction: number) => {
  if (!item.hasMultipleAnswers || !item.answers) return

  const newIndex = (item.currentAnswerIndex || 0) + direction
  if (newIndex >= 0 && newIndex < item.answers.length) {
    item.currentAnswerIndex = newIndex
    item.content = item.answers[newIndex].content
    item.id = item.answers[newIndex].id
  }
}

// 修改导出文件相关函数
const exportWord = async (id: string | number) => {
  const loading = showLoading({ title: '正在导出...' })
  try {
    const res = await generateWord(id)
    if (res.code === 20000) {
      const url = res.data.url
      const filename = decodeURIComponent(url.split('/').pop()?.split('?')[0] || 'export.docx')
      await downloadFile(url, filename)
    }
  } catch (error) {
    showToast({ title: '导出失败', icon: 'error' })
  } finally {
    loading.close()
  }
}

//  导出完整对话
const exportWordAll = async () => {
  showLoading({ title: '正在导出...' })
  if (!conversationId.value) {
    showToast({ title: '没有对话记录', icon: 'none' })
    return
  }
  const res = await generateWholeWord(conversationId.value)
  if (res.code === 20000) {
    const url = res.data.url
    const filename = decodeURIComponent(url.split('/').pop().split('?')[0])
    // 下载文件
    await downloadFile(url, filename)
  }
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    // 清除id
    conversationId.value = null
    chatInfo.value = {
      conversationId: '',
      type: ''
    }
    if (route.path === '/deepSeekR1') {
      pageData.value = {
        templateId: 1,
        type: 'directDialogue',
        title: '深度思考'
      }
      onPageInit()
    } else {
      pageData.value = null
      onPageInit()
    }
  },
  { immediate: true }
)

onMounted(async () => {
  const templateData = templateStore.templateData
  console.log('templateData', templateData)
  if (templateData) {
    pageData.value = templateData
  }
  if (route.path !== '/deepSeekR1' && !templateData) {
    const { id, templateId, requestId, type, title } = route.query
    pageData.value = {
      id,
      requestId,
      type,
      templateId,
      title
    }
    chatTitle.value = title
  }
  console.log('pageData.value', pageData.value)
  const userInfo: any = JSON.parse(localStorage.getItem('USER_INFO') || '{}')
  if (userInfo?.id) {
    try {
      await wsClient.connect(userInfo.id)
      console.log('WebSocket 连接已建立')
    } catch (error) {
      console.error('WebSocket 连接失败:', error)
    }
  }
  window.addEventListener('ws-message', handleWsMessage as any)
  await onPageInit()
  // scrollToLastMessage()
  // if (currentScrollTarget.value) {
  //   scrollToMessageById(currentScrollTarget.value)
  // }
})

onUnmounted(() => {
  // 清理数据
  templateStore.clearTemplateData()
  wsClient.close()
  conversationId.value = ''
  window.removeEventListener('ws-message', handleWsMessage as any)
})
</script>

<style lang="scss" scoped>
.chat-navbar {
  z-index: 1000;
  :deep(.u-status-bar) {
    background: rgba(255, 255, 255, 0.65) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
  }

  :deep(.u-navbar__content) {
    background: rgba(255, 255, 255, 0.65) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
  }
}

:deep(.chat-container) {
  background: linear-gradient(180deg, #f2fafc 0%, #e4f4ff 100%), /* 渐变背景 */ #e4f4ff !important; /* 纯色背景 */
  background-size: 100% 50%, 100% 50% !important; /* 各占一半 */
  background-position: top, bottom !important; /* 渐变在上，纯色在下 */
  background-repeat: no-repeat !important; /* 禁止背景重复 */
}

.chat-page {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background-color: transparent;
  position: relative;
}

.message-item-wrap {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 20px;
  flex: 1;
}

.message-item {
  display: flex;
  margin-bottom: 12px;
  position: relative;
  z-index: 1;
  width: 100%;

  .message-content {
    max-width: 80%;
    padding: 10px 15px;
    border-radius: 10px;
    background-color: #fff;
    word-break: break-all;

    // 确保内容正确展示
    .ai-message {
      width: 100%;

      :deep(p) {
        margin: 0;
        line-height: 1.5;
      }
    }

    .action-btn-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap; // 允许按钮换行
      gap: 8px;

      .answer-navigation {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .action-btn-group {
        display: inline-flex;
        align-items: center;
        justify-content: flex-end;
        gap: 16px;
        margin-left: auto;
      }
    }
  }

  &.message-user {
    flex-direction: row-reverse;

    .message-content {
      border-radius: 20px 0 20px 20px;
      background-color: #367cff;
      color: #fff;
      font-size: 16px;

      .message-text {
        white-space: pre-wrap; // 保留换行和空格
        word-break: break-word; // 允许在单词内换行
        line-height: 1.6; // 增加行间距
      }
    }
  }

  &.message-ai {
    .message-content {
      background-color: #fff;
      border-radius: 0 20px 20px 20px;

      // 错误消息样式
      &.error-message {
        background-color: #fff2f0;
        border: 1px solid #ffccc7;

        :deep(p) {
          color: #ff4d4f;
          margin: 0;
        }
      }
    }
  }
}

.popup-container {
  flex: 0 !important;
}

.popup-content {
  width: 90vw;
  height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  display: flex;
}

.list-icon {
  width: 60px;
  height: 60px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.retry-container {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  .retry-text {
    color: #ff4d4f;
    font-size: 20px;
    margin-left: 10px;
  }
}

.error-message {
  color: #ff4d4f !important;

  :deep(p) {
    color: #ff4d4f !important;
  }
}

:deep(.base-layout-content) {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; // 增加 iOS 滚动惯性
  display: flex;
  flex-direction: column;
  position: relative;
}

.message-item {
  scroll-margin-top: 20px;
}

.thinking-message {
  padding: 10px 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

// .input-area {
//   position: relative;
//   z-index: 100;
// }

.answer-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
