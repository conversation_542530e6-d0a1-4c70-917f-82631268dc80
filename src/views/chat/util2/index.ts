import { useRouter } from 'vue-router'

interface QueryData {
  type?: string
  id?: string | number
  [key: string]: any
}

interface ElementPosition {
  top: number
  bottom: number
  left: number
  right: number
  width: number
  height: number
}

interface DistanceResult {
  distance: number
  positionA: ElementPosition
  positionB: ElementPosition
}

export const jumpToChatRecord = (data?: QueryData) => {
  const router = useRouter()
  const queryString = data 
    ? '?' + Object.entries(data)
        .map(([key, value]) => `${key}=${value}`)
        .join('&') 
    : ''
    
  router.push({
    path: '/chat/history' + queryString
  })
}

export function calculateDistance(
  selectorA: string,
  selectorB: string,
  contextA: Element | null = null,
  contextB: Element | null = null
): Promise<DistanceResult> {
  return new Promise((resolve, reject) => {
    try {
      // 获取元素
      const elementA = contextA 
        ? contextA.querySelector(selectorA)
        : document.querySelector(selectorA)
      const elementB = contextB
        ? contextB.querySelector(selectorB)
        : document.querySelector(selectorB)

      if (!elementA || !elementB) {
        throw new Error(`元素未找到: ${!elementA ? selectorA : selectorB}`)
      }

      // 获取元素位置信息
      const rectA = elementA.getBoundingClientRect()
      const rectB = elementB.getBoundingClientRect()

      // 计算垂直距离
      const distance = Math.abs(rectB.top - rectA.bottom)

      resolve({
        distance,
        positionA: rectA,
        positionB: rectB
      })
    } catch (error) {
      reject(error)
    }
  })
}
