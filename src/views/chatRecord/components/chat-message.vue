<template>
  <div
    @click="messageTap"
    class="p-12px bg-white w-280px h-80px rounded-md cursor-pointer flex flex-col justify-between hover:shadow-md transition-shadow"
  >
    <!-- 标题区域 -->
    <div class="flex-1 min-h-0">
      <div class="text-14px font-medium text-gray-800 line-clamp-2 leading-5">
        {{ message || '未命名对话' }}
      </div>
    </div>

    <!-- 底部信息区域 -->
    <div class="flex justify-between items-center mt-8px">
      <div class="flex-1 min-w-0">
        <span class="text-11px text-gray-400 truncate">{{ time }}</span>
      </div>
      <div class="flex items-center gap-6px ml-8px">
        <div
          class="i-uiw-edit cursor-pointer text-14px text-gray-500 hover:text-blue-500 transition-colors"
          @click.stop="handleEdit"
          title="编辑标题"
        ></div>
        <div
          class="i-uiw-delete cursor-pointer text-14px text-gray-500 hover:text-red-500 transition-colors"
          @click.stop="handleDelete"
          title="删除对话"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { delConversation, updateConversation } from '@/api/aiTemplate'
import { useRouter } from 'vue-router'
import { useTemplateStore } from '@/stores/template'
import { Edit, Delete } from '@element-plus/icons-vue'
const templateStore = useTemplateStore()
const router = useRouter()

const emit = defineEmits(['refresh-chat-list'])

const props = defineProps({
  message: {
    type: [String, Number],
    required: true
  },
  timestamp: {
    type: String,
    required: false,
    default: ''
  },
  requestId: {
    type: String,
    required: false,
    default: ''
  },
  conversationId: {
    type: [String, Number],
    required: false,
    default: ''
  },
  title: {
    type: String,
    required: false,
    default: ''
  },
  time: {
    type: String,
    required: false,
    default: ''
  },
  delId: {
    type: [String, Number],
    required: false,
    default: ''
  },
  prompt: {
    type: String,
    required: false,
    default: ''
  },
  templateId: {
    type: [String, Number],
    required: false,
    default: ''
  }
})

const handleEdit = () => {
  ElMessageBox.prompt('请输入新的标题', '修改标题', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: props.title || '',
    inputPlaceholder: '请输入新的标题'
  })
    .then(async ({ value }) => {
      if (value && value !== props.title) {
        try {
          const result = await updateConversation(props.conversationId, value)
          if (result.code === 20000) {
            // 显示修改成功提示
            ElMessage({
              message: '修改成功',
              type: 'success',
              duration: 2000
            })
            emit('refresh-chat-list')
          } else {
            throw new Error(result.message || '修改失败')
          }
        } catch (error) {
          console.error('修改标题失败:', error)
          ElMessage({
            message: error.message || '修改失败',
            type: 'error',
            duration: 2000
          })
        }
      }
    })
    .catch(() => {})
}

const handleDelete = () => {
  // 先弹出确认框
  ElMessageBox.confirm('确定要删除这条聊天记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const result = await delConversation(props.delId)
        if (result.code === 20000) {
          ElMessage({
            message: '删除成功',
            type: 'success',
            duration: 1000
          })
          emit('refresh-chat-list')
        } else {
          throw new Error(result.message || '删除失败')
        }
      } catch (error) {
        ElMessage({
          message: error.message || '删除失败',
          type: 'error',
          duration: 1000
        })
      }
    })
    .catch(() => {})
}

const messageTap = () => {
  console.log(props.conversationId)
  if (props.templateId == 2) {
    router.push({
      path: '/app/mindMap',
      query: {
        conversationId: props.conversationId
      }
    })
  } else {
    const id = String(props.conversationId)
    templateStore.setTemplateData({
      id: id,
      conversationId: id,
      type: 'chatRecord',
      requestId: props.requestId
    })
    router.push({
      path: '/chat',
      query: {
        id: id,
        requestId: props.requestId,
        type: 'chatRecord'
      }
    })
  }
}
</script>
