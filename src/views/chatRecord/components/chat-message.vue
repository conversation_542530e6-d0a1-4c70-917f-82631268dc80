<template>
  <div
    @click="messageTap"
    class="p-10px bg-white w-300px h-130px rounded-md cursor-pointer flex flex-col justify-between"
  >
    <div>
      <span class="text-18px">{{ message }}</span>
      <div class="text-16px text-gray-500 mt-10px">
        <span v-if="prompt"> {{ prompt?.slice(0, 20) }}</span>
        <span v-else>空对话</span>
      </div>
    </div>

    <div class="flex justify-between items-center">
      <div>
        <span class="text-12px text-gray-400">{{ time }}</span>
      </div>
      <div>
        <div
          class="i-uiw-edit cursor-pointer text-16px text-gray-600 mr-10px"
          @click.stop="handleEdit"
        ></div>
        <div
          class="i-uiw-delete cursor-pointer text-16px text-gray-600"
          @click.stop="handleDelete"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { delConversation, updateConversation } from '@/api/aiTemplate'
import { useRouter } from 'vue-router'
import { useTemplateStore } from '@/stores/template'
import { Edit, Delete } from '@element-plus/icons-vue'
const templateStore = useTemplateStore()
const router = useRouter()

const emit = defineEmits(['refresh-chat-list'])

const props = defineProps({
  message: {
    type: [String, Number],
    required: true
  },
  timestamp: {
    type: String,
    required: false,
    default: ''
  },
  requestId: {
    type: String,
    required: false,
    default: ''
  },
  conversationId: {
    type: [String, Number],
    required: false,
    default: ''
  },
  title: {
    type: String,
    required: false,
    default: ''
  },
  time: {
    type: String,
    required: false,
    default: ''
  },
  delId: {
    type: [String, Number],
    required: false,
    default: ''
  },
  prompt: {
    type: String,
    required: false,
    default: ''
  },
  templateId: {
    type: [String, Number],
    required: false,
    default: ''
  }
})

const handleEdit = () => {
  ElMessageBox.prompt('请输入新的标题', '修改标题', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: props.title || '',
    inputPlaceholder: '请输入新的标题'
  })
    .then(async ({ value }) => {
      if (value && value !== props.title) {
        try {
          const result = await updateConversation(props.conversationId, value)
          if (result.code === 20000) {
            // 显示修改成功提示
            ElMessage({
              message: '修改成功',
              type: 'success',
              duration: 2000
            })
            emit('refresh-chat-list')
          } else {
            throw new Error(result.message || '修改失败')
          }
        } catch (error) {
          console.error('修改标题失败:', error)
          ElMessage({
            message: error.message || '修改失败',
            type: 'error',
            duration: 2000
          })
        }
      }
    })
    .catch(() => {})
}

const handleDelete = () => {
  // 先弹出确认框
  ElMessageBox.confirm('确定要删除这条聊天记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const result = await delConversation(props.delId)
        if (result.code === 20000) {
          ElMessage({
            message: '删除成功',
            type: 'success',
            duration: 1000
          })
          emit('refresh-chat-list')
        } else {
          throw new Error(result.message || '删除失败')
        }
      } catch (error) {
        ElMessage({
          message: error.message || '删除失败',
          type: 'error',
          duration: 1000
        })
      }
    })
    .catch(() => {})
}

const messageTap = () => {
  console.log(props.conversationId)
  if (props.templateId == 2) {
    router.push({
      path: '/app/mindMap',
      query: {
        conversationId: props.conversationId
      }
    })
  } else {
    const id = String(props.conversationId)
    templateStore.setTemplateData({
      id: id,
      conversationId: id,
      type: 'chatRecord',
      requestId: props.requestId
    })
    router.push({
      path: '/chat',
      query: {
        id: id,
        requestId: props.requestId,
        type: 'chatRecord'
      }
    })
  }
}
</script>
