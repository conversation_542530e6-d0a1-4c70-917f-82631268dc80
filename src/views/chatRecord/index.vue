<template>
  <div max-w-950px mx-auto py-10px>
    <div class="time-picker">
      <el-date-picker v-model="currentMonth" type="month" @change="handleDateConfirm" />
    </div>

    <div mt-20px grid grid-cols-3 gap-10px>
      <div v-for="(record, index) in chatRecords" :key="index">
        <chat-message
          :message="record.title || record.id"
          :timestamp="record.createdAt"
          :requestId="record.requestId"
          :conversationId="record.id"
          :title="record.title"
          :time="record.updateTime"
          :delId="record.id"
          :prompt="record.prompt"
          :templateId="record.templateId"
          @refresh-chat-list="refreshList"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, onUpdated } from 'vue'
import chatMessage from './components/chat-message.vue'
import { getChatRecordList } from '@/api/aiTemplate'
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
const currentMonth = ref(dayjs().format('YYYY-MM'))
const pageNo = ref(1)
const pageSize = ref(10)
const total = ref(0)

const chatRecords = ref<any>([])
const error = ref(null)
const loading = ref(false)

const route = useRoute() // 获取路由参数

const handleDateConfirm = (value: string) => {
  currentMonth.value = dayjs(value).format('YYYY-MM')
  pageNo.value = 1
  chatRecords.value = []
  loadRecords(currentMonth.value)
}

const loadRecords = async (month: string, isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      loading.value = true
    }
    const id: any = route.query.id
    const response = await getChatRecordList(month, pageNo.value, pageSize.value, id)
    if (response.code === 20000) {
      const { records, total: totalCount } = response.data
      total.value = totalCount
      chatRecords.value = records
    } else {
      throw new Error(response.message || '获取记录失败')
    }
  } catch (err: any) {
    error.value = err.message
    console.error('获取记录失败:', err)
  } finally {
    loading.value = false
  }
}

const refreshList = () => {
  console.log('刷新列表')
  pageNo.value = 1
  chatRecords.value = []
  loadRecords(currentMonth.value)
}

refreshList()
</script>
<style lang="scss" scoped>
.time-picker {
  :deep(.el-input__wrapper) {
    @apply bg-white shadow-none h-40px rounded-full;
  }
}
</style>
