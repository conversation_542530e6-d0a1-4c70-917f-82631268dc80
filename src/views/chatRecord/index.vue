<template>
  <div max-w-950px mx-auto py-10px>
    <div class="time-picker">
      <el-date-picker
        v-model="dateRange"
        type="monthrange"
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        @change="handleDateConfirm"
      />
    </div>

    <div mt-20px grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-12px>
      <div v-for="(record, index) in chatRecords" :key="index">
        <chat-message
          :message="record.title || record.id"
          :timestamp="record.createdAt"
          :requestId="record.requestId"
          :conversationId="record.id"
          :title="record.title"
          :time="record.updateTime"
          :delId="record.id"
          :prompt="record.prompt"
          :templateId="record.templateId"
          @refresh-chat-list="refreshList"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import chatMessage from './components/chat-message.vue'
import { getChatRecordList } from '@/api/aiTemplate'
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')

// 设置默认时间范围为最近3个月
const getDefaultDateRange = () => {
  const endMonth = dayjs()
  const startMonth = dayjs().subtract(2, 'month') // 最近3个月
  return [startMonth.toDate(), endMonth.toDate()]
}

const dateRange = ref(getDefaultDateRange())
const pageNo = ref(1)
const pageSize = ref(10)
const total = ref(0)

const chatRecords = ref<any>([])
const error = ref(null)
const loading = ref(false)

const route = useRoute() // 获取路由参数

const handleDateConfirm = (value: [Date, Date] | null) => {
  if (value && value.length === 2) {
    dateRange.value = value
    pageNo.value = 1
    chatRecords.value = []
    loadRecordsInRange(value[0], value[1])
  }
}

// 生成月份列表
const generateMonthList = (startDate: Date, endDate: Date): string[] => {
  const months: string[] = []
  const start = dayjs(startDate)
  const end = dayjs(endDate)

  let current = start
  while (current.isSame(end, 'month') || current.isBefore(end, 'month')) {
    months.push(current.format('YYYY-MM'))
    current = current.add(1, 'month')
  }

  return months
}

// 加载指定时间范围内的记录
const loadRecordsInRange = async (startDate: Date, endDate: Date, isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      loading.value = true
    }

    const id: any = route.query.id
    const months = generateMonthList(startDate, endDate)
    const allRecords: any[] = []

    // 并发请求所有月份的数据
    const promises = months.map(month =>
      getChatRecordList(month, 1, 100, id) // 每个月获取更多数据
    )

    const responses = await Promise.all(promises)

    responses.forEach(response => {
      if (response.code === 20000 && response.data?.records) {
        allRecords.push(...response.data.records)
      }
    })

    // 按时间排序（最新的在前面）
    allRecords.sort((a, b) => {
      const timeA = dayjs(a.updateTime || a.createdAt)
      const timeB = dayjs(b.updateTime || b.createdAt)
      return timeB.valueOf() - timeA.valueOf()
    })

    total.value = allRecords.length
    chatRecords.value = allRecords

  } catch (err: any) {
    error.value = err.message
    console.error('获取记录失败:', err)
  } finally {
    loading.value = false
  }
}

// 保留原有的单月加载函数以兼容其他地方的调用
const loadRecords = async (month: string, isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      loading.value = true
    }
    const id: any = route.query.id
    const response = await getChatRecordList(month, pageNo.value, pageSize.value, id)
    if (response.code === 20000) {
      const { records, total: totalCount } = response.data
      total.value = totalCount
      chatRecords.value = records
    } else {
      throw new Error(response.message || '获取记录失败')
    }
  } catch (err: any) {
    error.value = err.message
    console.error('获取记录失败:', err)
  } finally {
    loading.value = false
  }
}

const refreshList = () => {
  console.log('刷新列表')
  pageNo.value = 1
  chatRecords.value = []
  if (dateRange.value && dateRange.value.length === 2) {
    loadRecordsInRange(dateRange.value[0], dateRange.value[1])
  }
}

// 初始化加载最近3个月的数据
refreshList()
</script>
<style lang="scss" scoped>
.time-picker {
  :deep(.el-input__wrapper) {
    @apply bg-white shadow-none h-40px rounded-full;
  }
}

// 添加文本截断样式
:deep(.line-clamp-2) {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
}
</style>
