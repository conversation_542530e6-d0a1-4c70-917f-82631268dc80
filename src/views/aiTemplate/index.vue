<template>
  <div class="template-page">
    <div class="form-container">
      <ai-dynamic-form v-if="id" ref="dynamicFormRef" :template-id="Number(id)" />
    </div>
    <div class="footer">
      <el-button type="default" @click="jumpToChatRecord({ type: 'template', id })">
        <el-icon mr-10px><Timer /></el-icon>
        历史记录
      </el-button>
      <el-button type="primary" @click="submitForm" :loading="loading"> 一键生成 </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import aiDynamicForm from './components/aiDynamicForm.vue'
import { pickBy, isEmpty } from 'lodash-es'
import { Timer } from '@element-plus/icons-vue'
import { useTemplateStore } from '@/stores/template'

const route = useRoute()
const router = useRouter()
const dynamicFormRef = ref(null)
const id = ref(undefined)
const title = ref(undefined)
const loading = ref(false)
const templateStore = useTemplateStore()

onMounted(() => {
  const queryId = route.query.id
  if (!queryId) {
    ElMessage.error('模板ID不能为空')
    router.push('/')
    return
  }
  id.value = queryId
  title.value = route.query.title
})

const buildSubmitData = (validateData, formDataWithDefaultValue) => {
  return {
    ...formDataWithDefaultValue,
    ...pickBy(validateData, (value) => !isEmpty(value))
  }
}

const jumpToChatRecord = ({ type, id }) => {
  router.push({
    path: '/chatRecord',
    query: {
      type,
      id
    }
  })
}

const submitForm = async () => {
  try {
    loading.value = true
    if (!dynamicFormRef.value) {
      ElMessage.error('表单未加载完成')
      return
    }
    const validateData = await dynamicFormRef.value.validate().catch((err) => {
      ElMessage.warning('请填写完整的表单信息')
      throw err
    })

    let templateValue = []
    const submitData = buildSubmitData(validateData, dynamicFormRef.value.formDataWithDefaultValue)
    for (const [key, value] of Object.entries(submitData)) {
      if (Array.isArray(value)) {
        templateValue.push({
          name: key,
          value: value.join(',')
        })
      } else {
        templateValue.push({
          name: key,
          value: value
        })
      }
    }

    // 存储到 pinia
    templateStore.setTemplateData({
      templateId: id.value,
      type: 'template',
      templateValue,
      title: title.value
    })

    // 跳转时只带必要的参数
    router.push({
      path: '/chat',
      query: {
        type: 'template',
        id: id.value
      }
    })
  } catch (error) {
    console.error('提交失败：', error)
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.template-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.form-container {
  flex: 1;
  overflow-y: auto;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  margin-top: 20px;
  border-top: 1px solid #eee;
}
</style>
