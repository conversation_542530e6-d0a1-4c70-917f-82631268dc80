<template>
  <el-form 
    ref="formRef"
    :model="formData"
    label-position="top"
    :rules="rules"
  >
    <div class="form-item-container" v-for="(item, index) in formConfig" :key="index">
      <el-form-item :label="item.keyName" :prop="item.keyName">
        <!-- Input类型 -->
        <el-input
          v-if="item.type === 'input'"
          v-model="formData[item.keyName]"
          :placeholder="item.tip"
          type="textarea"
          :rows="4"
        />

        <!-- Select类型 -->
        <el-select
          v-if="item.type === 'select'"
          v-model="formData[item.keyName]"
          :placeholder="item.tip"
          style="width: 100%"
        >
          <el-option
            v-for="option in parseOptions(item.extend)"
            :key="option.value"
            :label="option.text"
            :value="option.value"
          />
        </el-select>

        <!-- MultiSelect类型 -->
        <el-select
          v-if="item.type === 'multiSelect'"
          v-model="formData[item.keyName]"
          :placeholder="item.tip"
          multiple
          style="width: 100%"
        >
          <el-option
            v-for="option in parseOptions(item.extend)"
            :key="option.value"
            :label="option.text"
            :value="option.value"
          />
        </el-select>

        <!-- Uploader类型 -->
        <el-upload
          v-if="item.type === 'uploader'"
          v-model:file-list="formData[item.keyName]"
          :placeholder="item.tip"
          action="/upload"
          multiple
        >
          <el-button type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">{{ item.tip }}</div>
          </template>
        </el-upload>
      </el-form-item>
    </div>
  </el-form>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { getAiTemplateItem } from '@/api/aiTemplate'

const props = defineProps({
  templateId: {
    type: Number,
    required: true
  }
})

const formRef = ref(null)
const formConfig = ref([])
const formData = ref({})
const formDataWithDefaultValue = ref({})
const rules = ref({})

// 解析选项配置
const parseOptions = (extend) => {
  let options = []
  if (typeof extend === 'string') {
    try {
      options = extend.split(',')
    } catch (e) {
      options = []
    }
  } else if (Array.isArray(extend)) {
    options = extend
  }

  return options.map((item) => ({
    value: item,
    text: item
  }))
}

// 初始化表单数据
const initFormData = (config) => {
  const data = {}
  // 尝试从localStorage获取保存的数据
  const savedData = localStorage.getItem(`form_data_${props.templateId}`)
  const parsedSavedData = savedData ? JSON.parse(savedData) : {}

  config.forEach((item) => {
    if (item.type === 'multiSelect') {
      // 使用保存的数据或空数组
      data[item.keyName] = parsedSavedData[item.keyName] || []
      formDataWithDefaultValue.value[item.keyName] = item.defaultValue ?? []
    } else {
      // 使用保存的数据或空字符串
      data[item.keyName] = parsedSavedData[item.keyName] || ''
      formDataWithDefaultValue.value[item.keyName] = item.defaultValue ?? ''
    }
  })
  formData.value = data
}

// 保存表单数据到localStorage
const saveFormData = () => {
  localStorage.setItem(`form_data_${props.templateId}`, JSON.stringify(formData.value))
}

// 获取表单配置
const getFormConfig = async () => {
  try {
    console.log('templateId:', props.templateId) // 调试用
    const res = await getAiTemplateItem(props.templateId)
    if (!res.data) {
      ElMessage.error('获取模板配置失败')
      return
    }
    formConfig.value = res.data || []
    initFormData(formConfig.value)
    generateRules(formConfig.value)
  } catch (error) {
    console.error('获取表单配置失败：', error)
    ElMessage.error('获取表单配置失败')
  }
}

// 生成验证规则
const generateRules = (config) => {
  const rulesObj = {}

  config.forEach((item) => {
    if (item.required === 1) {
      rulesObj[item.keyName] = [{
        required: true,
        message: `请${item.type === 'select' ? '选择' : '输入'}${item.keyName}`,
        trigger: item.type === 'select' ? 'change' : ['blur', 'change']
      }]

      if (item.type === 'multiSelect') {
        rulesObj[item.keyName][0].type = 'array'
        rulesObj[item.keyName][0].message = `请至少选择一个${item.keyName}`
      }
    }
  })

  rules.value = rulesObj
}

onMounted(() => {
  getFormConfig()
})

// 定义验证方法
const validate = () => {
  return new Promise((resolve, reject) => {
    if (!formRef.value) {
      reject(new Error('表单实例不存在'))
      return
    }

    formRef.value.validate((valid, fields) => {
      if (valid) {
        // 只在表单验证通过时保存数据
        saveFormData()
        resolve(formData.value)
      } else {
        console.log('验证失败字段:', fields)
        reject(fields)
      }
    })
  })
}

// 暴露方法给父组件
defineExpose({
  formRef,
  formData,
  validate,
  formDataWithDefaultValue
})
</script>

<style lang="scss" scoped>
.form-item-container {
  background: #ffffff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
