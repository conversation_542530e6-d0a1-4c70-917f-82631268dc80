import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'aiAssistant',
        component: () => import('@/views/aiAssistant/index.vue')
      },
      {
        path: 'ai-assistant',
        redirect: '/'
      },
      {
        path: '/chat',
        name: 'chat',
        component: () => import('@/views/chat/index.vue')
      },
      {
        path: 'ai-template',
        name: 'aiTemplate',
        component: () => import('@/views/aiTemplate/index.vue')
      },
      {
        path: '/chatRecord',
        name: 'chatRecord',
        component: () => import('@/views/chatRecord/index.vue')
      },
      {
        path: '/app/mindMap',
        name: 'mindMap',
        component: () => import('@/views/mindMap/index.vue')
      },
      {
        path: '/deepSeekR1',
        name: 'deepSeekR1',
        component: () => import('@/views/chat/index.vue')
      },
      {
        path: '/user',
        name: 'user',
        component: () => import('@/views/user/index.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
