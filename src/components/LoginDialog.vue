<template>
  <el-dialog
    v-model="dialogVisible"
    title="登录"
    width="400px"
    :close-on-click-modal="false"
    :show-close="true"
    class="login-dialog"
  >
    <div class="p-20px">
      <!-- 扫码登录 -->
      <div class="qrcode-login flex flex-col items-center">
        <div class="qrcode-container border border-gray-200 p-4 mb-4 relative">
          <!-- 二维码图片 -->
          <div class="w-60 h-60 bg-gray-100 flex items-center justify-center">
            <img
              v-if="qrcodeUrl"
              :src="qrcodeUrl"
              alt="登录二维码"
              class="w-full h-full object-contain"
            />
            <el-icon v-else class="text-4xl text-gray-400"><Picture /></el-icon>
          </div>
        </div>

        <!-- 状态提示 -->
        <p class="text-gray-500 text-sm">
          {{
            qrStatus === 10001
              ? '二维码已失效，请刷新'
              : qrStatus === 10003
              ? '使用微信扫一扫登录'
              : ''
          }}
        </p>

        <el-button text class="mt-2" @click="refreshQrcode" :disabled="qrStatus === 10004">
          刷新二维码
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, watch, onUnmounted } from 'vue'
import { Picture } from '@element-plus/icons-vue'
import { generateMpQrcode, checkMpLogin } from '@/api/login'
import { setToken } from '@/utils/token'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'login-success'])

// 控制弹窗显示
const dialogVisible = ref(props.visible)

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
  }
)

// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (newVal) => {
    emit('update:visible', newVal)
  }
)

// 二维码登录相关
const qrcodeUrl = ref('')
const qrToken = ref('')
const qrStatus = ref(10003) // 默认等待扫码
const pollingTimer = ref<number>(0)

// 获取二维码
const getQrcode = async (type: number = 0) => {
  try {
    const res = await generateMpQrcode()
    if (res.code === 20000 && res.data) {
      qrToken.value = res.data.qrToken
      qrcodeUrl.value = res.data.qrUrl
      // 开始轮询状态
      startPolling(type)
    } else {
      ElMessage.error('获取二维码失败')
    }
  } catch (error) {
    console.error('获取二维码失败:', error)
    ElMessage.error('获取二维码失败，请重试')
  }
}

// 开始轮询
const startPolling = (type: number = 0) => {
  // 清除之前的轮询
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
  }

  // 开始新的轮询
  pollingTimer.value = setInterval(async () => {
    try {
      let res: any = null
      if (type === 0) {
        res = await checkMpLogin({ qrToken: qrToken.value })
      } else {
        res = await checkMpLogin({ qrToken: qrToken.value, continueLogin: 'Y' })
      }

      // 根据状态码处理不同情况
      switch (res.code) {
        case 20000: // 登录成功
          // 保存token和用户信息
          if (res.data?.token) {
            setToken(res.data.token)
            localStorage.setItem('USER_INFO', JSON.stringify(res.data.userInfo))
            // 停止轮询
            clearInterval(pollingTimer.value)
            ElMessage.success('登录成功')
            dialogVisible.value = false
            emit('login-success', res.data)
          }
          break

        case 20011: // 新增的状态码处理
          // 停止当前轮询
          clearInterval(pollingTimer.value)

          // 使用 ElMessageBox 显示确认弹窗
          ElMessageBox.confirm(res.message || '是否重新获取登录二维码？', '提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning'
          })
            .then(async () => {
              // 用户点击"是"，重新获取二维码
              await getQrcode(1)
            })
            .catch(() => {
              // 用户点击"否"，关闭登录弹窗
              dialogVisible.value = false
            })
          break

        case 10001: // 二维码失效
          clearInterval(pollingTimer.value)
          ElMessage.error('二维码已失效')
          refreshQrcode() // 重新生成二维码
          break

        case 10003: // 等待扫码
          qrStatus.value = 10003
          break
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
    }
  }, 1000) // 每秒轮询一次
}

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = 0
  }
}

// 刷新二维码
const refreshQrcode = () => {
  qrcodeUrl.value = ''
  qrToken.value = ''
  qrStatus.value = 10003
  getQrcode()
}

// 组件卸载时清除轮询
onUnmounted(() => {
  stopPolling()
})

// 组件挂载时生成二维码
onMounted(() => {
  getQrcode()
})

// 监听弹窗关闭
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时停止轮询
    stopPolling()
  }
})
</script>

<style lang="scss" scoped>
.login-dialog :deep(.el-dialog__header) {
  text-align: center;
  font-weight: bold;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.qrcode-container {
  border-radius: 4px;
  position: relative;
}

.qr-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}

.mask-content {
  text-align: center;
}
</style>
