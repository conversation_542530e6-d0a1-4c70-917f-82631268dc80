let baseURL =
  import.meta.env.MODE === 'development'
    ? 'https://aih.mypacelab.com/boot'
    : 'https://aih.mypacelab.com/boot'

// WebSocket 相关类型定义
export interface WsMessage {
  key: string
  value: string
  error?: boolean
  end?: boolean
  questionId?: string | number
  conversationId?: string
  reason?: boolean
}

interface WsResponse {
  data: string
}

interface WsOptions {
  url: string
  success?: () => void
  fail?: (error: Error) => void
}

interface WsInstance {
  send: (options: { data: string; fail?: (error: Error) => void }) => void
  onOpen: (callback: () => void) => void
  onClose: (callback: () => void) => void
  onError: (callback: (error: Error) => void) => void
  onMessage: (callback: (res: WsResponse) => void) => void
  close: (options?: {
    success?: () => void
    fail?: (error: Error) => void
    complete?: () => void
  }) => void
}

class WebSocketClient {
  private ws: WebSocket | null = null
  private userId: string | null = null
  private isConnecting: boolean = false
  private reconnectAttempts: number = 0
  private readonly maxReconnectAttempts: number = 100
  private readonly reconnectTimeout: number = 3000
  private heartbeatTimer: number | null = null
  private readonly heartbeatInterval: number = 30000
  private lastHeartbeatResponse: number = Date.now()
  private readonly heartbeatTimeout: number = 35000
  private connected: boolean = false
  private manuallyDisconnected: boolean = false

  isConnected(): boolean {
    return this.connected && this.ws !== null
  }

  connect(userId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || this.isConnected()) {
        resolve()
        return
      }

      this.manuallyDisconnected = false
      
      this.userId = userId
      if (!this.userId) {
        reject(new Error('未获取到用户ID'))
        return
      }

      const wsUrl = `${baseURL.replace('http', 'ws')}/websocket/${this.userId}`

      try {
        // 创建 WebSocket 连接
        const ws = new WebSocket(wsUrl)

        ws.onopen = () => {
          console.log('WebSocket 连接已打开')
          this.isConnecting = false
          this.reconnectAttempts = 0
          this.connected = true
          this.startHeartbeat()
          resolve()
        }

        ws.onclose = () => {
          console.log('WebSocket 连接已关闭')
          this.handleClose()
        }

        ws.onerror = (error: any) => {
          console.error('WebSocket 错误:', error)
          this.handleError(error)
          reject(error)
        }

        ws.onmessage = (event) => {
          try {
            if (event.data === 'heartcheck') {
              this.lastHeartbeatResponse = Date.now()
              return
            }
            const data = JSON.parse(event.data)
            // 使用自定义事件替代 uni.$emit
            window.dispatchEvent(new CustomEvent('ws-message', { detail: data }))
          } catch (error) {
            console.error('解析 WebSocket 消息失败:', error)
          }
        }

        this.ws = ws
      } catch (error) {
        console.error('创建 WebSocket 连接失败:', error)
        reject(error)
      }
    })
  }

  handleClose(): void {
    this.isConnecting = false
    this.ws = null
    this.stopHeartbeat()
    this.connected = false
    
    if (!this.manuallyDisconnected) {
      this.reconnect()
    }
  }

  handleError(error: Error): void {
    this.isConnecting = false
    this.ws = null
    this.connected = false
    this.stopHeartbeat()
    // 使用自定义事件替代 uni.$emit
    window.dispatchEvent(new CustomEvent('ws-error', { detail: error }))
    
    if (!this.manuallyDisconnected) {
      this.reconnect()
    }
  }

  reconnect(): void {
    if (this.manuallyDisconnected || this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('不进行重连：手动断开或达到最大重连次数')
      return
    }

    this.reconnectAttempts++
    console.log(`${this.reconnectTimeout / 1000}秒后尝试第${this.reconnectAttempts}次重连`)

    setTimeout(() => {
      if (this.userId && !this.manuallyDisconnected) {
        this.connect(this.userId)
      }
    }, this.reconnectTimeout)
  }

  send(data: WsMessage): void {
    if (!this.isConnected()) {
      console.error('WebSocket 未连接')
      if (!this.manuallyDisconnected) {
        this.reconnect()
      }
      return
    }

    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data)
      this.ws?.send(message)
    } catch (error) {
      console.error('发送消息失败:', error)
      if (!this.manuallyDisconnected) {
        this.reconnect()
      }
    }
  }

  startHeartbeat(): void {
    this.stopHeartbeat()
    this.lastHeartbeatResponse = Date.now()

    this.heartbeatTimer = window.setInterval(() => {
      if (!this.isConnected() || this.manuallyDisconnected) {
        this.stopHeartbeat()
        return
      }

      const now = Date.now()
      if (now - this.lastHeartbeatResponse > this.heartbeatTimeout) {
        console.log('心跳超时，开始重连')
        if (!this.manuallyDisconnected) {
          this.reconnect()
        }
        return
      }

      this.send({ key: 'heartcheck', value: 'heartcheck' })
    }, this.heartbeatInterval)
  }

  stopHeartbeat(): void {
    if (this.heartbeatTimer !== null) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  close(): void {
    this.manuallyDisconnected = true
    
    this.stopHeartbeat()
    if (this.ws) {
      try {
        this.ws.close()
        console.log('WebSocket 连接已主动关闭')
      } catch (error) {
        console.error('关闭 WebSocket 连接出错:', error)
      } finally {
        this.isConnecting = false
        this.userId = null
        this.ws = null
        this.reconnectAttempts = 0
        this.connected = false
      }
    }
  }
}

export const wsClient = new WebSocketClient()
