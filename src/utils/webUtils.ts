import { ElMessage, ElLoading } from 'element-plus'

// 替代 uni.showToast
export const showToast = (options: {
  title: string
  icon?: 'success' | 'warning' | 'error' | 'none'
  duration?: number
}) => {
  const type = options.icon === 'none' ? 'info' : options.icon || 'success'
  ElMessage({
    message: options.title,
    type,
    duration: options.duration || 2000
  })
}

// 替代 uni.showLoading
export const showLoading = (options: { title: string; mask?: boolean }) => {
  return ElLoading.service({
    text: options.title,
    background: options.mask ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.3)'
  })
}

// 替代 uni.hideLoading
export const hideLoading = () => {
  ElLoading.service().close()
}

// 替代 uni.setClipboardData
export const setClipboardData = async (options: {
  data: string
  success?: () => void
  fail?: (error: Error) => void
}) => {
  try {
    await navigator.clipboard.writeText(options.data)
    options.success?.()
  } catch (error) {
    options.fail?.(error as Error)
  }
}

// 替代 uni.downloadFile
export const downloadFile = async (url: string, filename: string) => {
  try {
    const response = await fetch(url)
    const blob = await response.blob()
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('下载失败:', error)
    showToast({ title: '下载失败', icon: 'error' })
  }
}

// 替代 storage 相关操作
export const storage = {
  set: (key: string, data: any) => {
    localStorage.setItem(key, JSON.stringify(data))
  },
  get: (key: string) => {
    const data = localStorage.getItem(key)
    try {
      return data ? JSON.parse(data) : null
    } catch {
      return null
    }
  },
  remove: (key: string) => {
    localStorage.removeItem(key)
  }
}
