import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { getToken, removeToken } from '@/utils/token'
import router from '@/router'
import { ElMessage } from 'element-plus'

// 定义不需要token的接口白名单
const whiteList: string[] = ['/home/<USER>', '/home/<USER>']

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 15000
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    const token = getToken()

    // 检查当前请求URL是否在白名单中
    const url = config.url || ''
    const isWhitelisted = whiteList.some((item) => url.includes(item))

    // 只有不在白名单中的请求才添加token
    if (token && !isWhitelisted) {
      config.headers.Authorization = token
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data
    // 处理token失效的情况
    if (res.code === 20010 && res.message.includes('Token失效')) {
      ElMessage.error('登录已过期，请重新登录')
      removeToken() // 清除token
      router.push('/') // 返回首页
      return Promise.reject(new Error(res.message))
    }

    return res
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 封装请求方法
const request = async <T = any>(config: AxiosRequestConfig): Promise<T> => {
  try {
    const response = await service(config)
    return response as T
  } catch (error) {
    return Promise.reject(error)
  }
}

export default request
