import { useStorage } from '@vueuse/core'
const TOKEN_KEY = 'TOKEN'

export const tokenStorage = useStorage<string | null>(TOKEN_KEY, null)

/**
 * 获取token
 */
export const getToken = (): string | null => {
  return tokenStorage.value
}

/**
 * 设置token
 * @param token - token字符串
 */
export const setToken = (token: any): void => {
  tokenStorage.value = token
}

/**
 * 删除token
 */
export const removeToken = (): void => {
  tokenStorage.value = null
}

/**
 * 判断是否有token
 */
export const hasToken = (): boolean => {
  return !!getToken()
}
