import { ElMessage, ElLoading } from 'element-plus'

// 类型定义
interface QueryData {
  type?: string
  id?: string | number
  [key: string]: any
}

interface ElementPosition {
  top: number
  bottom: number
  left: number
  right: number
  width: number
  height: number
}

interface DistanceResult {
  distance: number
  positionA: ElementPosition
  positionB: ElementPosition
}

interface DebouncedFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): void
  cancel: () => void
}

// Toast 相关函数
export const showToast = (options: {
  title: string
  icon?: 'success' | 'warning' | 'error' | 'none'
  duration?: number
}) => {
  const type = options.icon === 'none' ? 'info' : options.icon || 'success'
  ElMessage({
    message: options.title,
    type,
    duration: options.duration || 2000
  })
}

// Loading 相关函数
export const showLoading = (options: { title: string; mask?: boolean }) => {
  return ElLoading.service({
    text: options.title,
    background: options.mask ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.3)'
  })
}

export const hideLoading = () => {
  ElLoading.service().close()
}

// 剪贴板相关函数
export const setClipboardData = async (options: {
  data: string
  success?: () => void
  fail?: (error: Error) => void
}) => {
  try {
    await navigator.clipboard.writeText(options.data)
    options.success?.()
    showToast({ title: '复制成功', icon: 'success' })
  } catch (error) {
    options.fail?.(error as Error)
    showToast({ title: '复制失败', icon: 'error' })
  }
}

// 文件下载相关函数
export const downloadFile = async (url: string, filename: string) => {
  try {
    const response = await fetch(url)
    const blob = await response.blob()
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('下载失败:', error)
    showToast({ title: '下载失败', icon: 'error' })
  }
}

// 存储相关函数
export const storage = {
  set: (key: string, data: any) => {
    localStorage.setItem(key, JSON.stringify(data))
  },
  get: (key: string) => {
    const data = localStorage.getItem(key)
    try {
      return data ? JSON.parse(data) : null
    } catch {
      return null
    }
  },
  remove: (key: string) => {
    localStorage.removeItem(key)
  }
}

// URL 参数解析函数
export const parseParams = (...keys: string[]): (string | undefined)[] => {
  const query = new URLSearchParams(window.location.search)
  return keys.map((key) => query.get(key) || undefined)
}

// 日期格式化函数
export const formatDate = (date: string | number): number => {
  if (typeof date === 'string') {
    const formattedDate = date.replace(/\s/, 'T')
    return new Date(formattedDate).getTime()
  }
  return date
}

// 文件大小格式化函数
export const formatFileSize = (size: number): string => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(2)}${units[index]}`
}

// 计算元素距离
export const calculateDistance = (
  selectorA: string,
  selectorB: string,
  contextA: Element | null = null,
  contextB: Element | null = null
): Promise<DistanceResult> => {
  return new Promise((resolve, reject) => {
    try {
      const elementA = contextA
        ? contextA.querySelector(selectorA)
        : document.querySelector(selectorA)
      const elementB = contextB
        ? contextB.querySelector(selectorB)
        : document.querySelector(selectorB)

      if (!elementA || !elementB) {
        throw new Error(`元素未找到: ${!elementA ? selectorA : selectorB}`)
      }

      const rectA = elementA.getBoundingClientRect()
      const rectB = elementB.getBoundingClientRect()

      const distance = Math.abs(rectB.top - rectA.bottom)

      resolve({
        distance,
        positionA: rectA,
        positionB: rectB
      })
    } catch (error) {
      reject(error)
    }
  })
}
