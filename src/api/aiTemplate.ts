import request from '@/utils/request'

// API 返回类型定义
interface ApiResponse<T> {
  code: number
  data: T
  message: string
}

// 获取ai模版列表
export const getAiTemplateList = (params: Record<string, any>) => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/listTemplate',
    method: 'GET',
    params
  })
}

// 获取ai模版列表 不需要token
export const getAiTemplateListNoToken = (params: Record<string, any>) => {
  return request<ApiResponse<any>>({
    url: '/boot/home/<USER>',
    method: 'GET',
    params
  })
}

// 获取ai模版详情
export const getAiTemplateItem = (id: string | number) => {
  return request<ApiResponse<any>>({
    url: `/boot/chat/getTemplateDetail/${id}`,
    method: 'GET'
  })
}

// 创建对话
export const createByAiTemplate = (data: any) => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/createTemplateRecord',
    method: 'POST',
    data
  })
}

// 创建初始对话
export const initialChat = (data: any) => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/createConversation',
    method: 'POST',
    data
  })
}

// 开始对话
export const startChat = (data: any) => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/sendQuestion',
    method: 'POST',
    data
  })
}

// 获取聊天记录列表
export const getChatRecordList = (month: string, pageNo = 1, pageSize = 10, templateId = '') => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/listConversation',
    method: 'GET',
    params: {
      month,
      pageNo,
      pageSize,
      templateId
    }
  })
}

// 获取当前聊天的聊天记录
export const getCurrentChatRecord = (conversationId: string | number) => {
  return request<ApiResponse<any>>({
    url: `/boot/chat/listConversation/${conversationId}`,
    method: 'GET'
  })
}

// 获取当前模版的使用记录
export const getAiTemplateRecordList = (
  templateId: string | number,
  month: string,
  pageNo = 1,
  pageSize = 10
) => {
  return request<ApiResponse<any>>({
    url: `/boot/chat/listTemplateRecord/${templateId}`,
    method: 'GET',
    params: {
      month,
      pageNo,
      pageSize
    }
  })
}

// 提交反馈
export const submitFeedback = (questionId: string | number) => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/commentQuestion',
    method: 'POST',
    data: {
      commentTxt: '',
      questionId,
      comment: '2'
    }
  })
}

// 生成word
export const generateWord = (questionId: string | number) => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/generateWord',
    method: 'GET',
    params: {
      questionId
    }
  })
}

// 删除聊天记录
export const delConversation = (conversationId: string | number) => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/delConversation',
    method: 'GET',
    params: {
      conversationId
    }
  })
}

// 修改聊天标题
export const updateConversation = (conversationId: string | number, title: string) => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/updateConversation',
    method: 'POST',
    data: {
      conversationId,
      title
    }
  })
}

// 重新生成回答
export const regenerateAnswer = (questionId: string | number) => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/regenerateAnswer',
    method: 'GET',
    params: {
      questionId
    }
  })
}

// 生成完整word
export const generateWholeWord = (conversationId: string | number) => {
  return request<ApiResponse<any>>({
    url: '/boot/chat/generateWholeWord',
    method: 'GET',
    params: {
      conversationId
    }
  })
}

// 获取模板分类 /boot/home/<USER>
export const getTemplateGroup = () => {
  return request<ApiResponse<any>>({
    url: '/boot/home/<USER>',
    method: 'GET'
  })
}
