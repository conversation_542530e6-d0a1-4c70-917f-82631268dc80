import request from '@/utils/request'

// 发送短信 /boot/auth/sendMsg
export const sendMsg = (params: Record<string, any>) => {
  const { phone, ...bodyParams } = params
  return request<any>({
    url: '/boot/auth/sendMsg',
    method: 'POST',
    params: { phone },
    data: bodyParams
  })
}

// 登录  /boot/auth/loginByPhone
export const loginByPhone = (params: Record<string, any>) => {
  return request<any>({
    url: '/boot/auth/loginByPhone',
    method: 'POST',
    data: params
  })
}

// 获取二维码token  /boot/auth/generateQrcode
export const generateQrcode = () => {
  return request<any>({
    url: '/boot/auth/generateQrcode',
    method: 'GET'
  })
}
// 轮训获取二维码状态  /boot/auth/checkLogin qrToken
export const checkLogin = (params: Record<string, any>) => {
  return request<any>({
    url: `/boot/auth/checkLogin`,
    method: 'GET',
    params
  })
}

// 修改用户昵称 /boot/user/updateUser
export const updateUser = (params: Record<string, any>) => {
  return request<any>({
    url: `/boot/user/updateUser`,
    method: 'POST',
    data: params
  })
}

// 获取用户信息 /boot/user/checkToken
export const checkToken = () => {
  return request<any>({
    url: `/boot/user/checkToken`,
    method: 'GET'
  })
}

// 公众号登录二维码 /boot/auth/generateMpQrcode
export const generateMpQrcode = () => {
  return request<any>({
    url: `/boot/auth/generateMpQrcode`,
    method: 'GET'
  })
}
// 检查公众号登录状态 /boot/auth/checkMpLogin
export const checkMpLogin = (params: Record<string, any>) => {
  return request<any>({
    url: `/boot/auth/checkMpLogin`,
    method: 'GET',
    params
  })
}
