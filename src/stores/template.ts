import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { TemplateData } from '@/types/template'

export const useTemplateStore = defineStore('template', () => {
  const templateData = ref<TemplateData | null>(null)

  const setTemplateData = (data: TemplateData) => {
    templateData.value = data
  }

  const clearTemplateData = () => {
    templateData.value = null
  }

  return {
    templateData,
    setTemplateData,
    clearTemplateData
  }
})
