import { createApp, h } from 'vue'
import type { App } from 'vue'
import LoginDialog from '@/components/LoginDialog.vue'

export interface LoginOptions {
  onSuccess?: (data: any) => void
  onCancel?: () => void
}

export const LoginDialogPlugin = {
  install(app: App) {
    // 添加全局方法
    app.config.globalProperties.$login = (options?: LoginOptions) => {
      showLoginDialog(options)
    }
  }
}

// 创建一个可以在任意地方调用的方法
let loginInstance: any = null

export const showLoginDialog = (options?: LoginOptions) => {
  // 如果已经有实例，先移除
  if (loginInstance) {
    document.body.removeChild(loginInstance.$el)
    loginInstance = null
  }

  // 创建一个挂载点
  const mountNode = document.createElement('div')
  document.body.appendChild(mountNode)

  // 创建组件实例
  const Instance = createApp({
    render() {
      return h(LoginDialog, {
        visible: true,
        'onUpdate:visible': (val: boolean) => {
          if (!val) {
            // 关闭弹窗时销毁实例
            setTimeout(() => {
              Instance.unmount()
              document.body.removeChild(mountNode)
              loginInstance = null
              options?.onCancel?.()
            }, 300)
          }
        },
        'onLogin-success': (data: any) => {
          options?.onSuccess?.(data)
        }
      })
    }
  })

  // 挂载组件
  loginInstance = Instance.mount(mountNode)

  return loginInstance
}

// 导出一个可以直接使用的方法
export const useLogin = () => {
  return {
    showLogin: showLoginDialog
  }
}

export default LoginDialogPlugin 
