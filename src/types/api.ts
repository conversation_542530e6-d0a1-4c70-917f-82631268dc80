// API 响应的通用接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 聊天记录的接口
export interface ChatRecord {
  id: string | number
  role: 'user' | 'assistant'
  message: string
  messageType?: string
  reason?: string
  reasonContent?: string
  createTime: string
  answerId?: string | number
  question?: string
  answer?: string
  content?: string
  contentRes?: string
}

// 组件引用的接口
export interface ChatInputRef {
  isSendDisabled: boolean
}

export interface ToastRef {
  show: (options: { message: string; type: string }) => void
}
