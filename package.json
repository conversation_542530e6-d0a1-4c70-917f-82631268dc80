{"name": "ai-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/crypto-js": "^4.2.2", "@vueuse/core": "^12.7.0", "axios": "^1.7.9", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "element-plus": "^2.9.4", "file-saver": "^2.0.5", "html-to-image": "^1.11.13", "lodash-es": "^4.17.21", "markmap-common": "^0.18.9", "markmap-lib": "^0.18.11", "markmap-view": "^0.18.10", "md-editor-v3": "^5.3.2", "mitt": "^3.0.1", "pinia": "^3.0.1", "qrcode.vue": "^3.6.0", "vue": "^3.5.13", "vue-router": "4", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@iconify-json/solar": "^1.2.2", "@iconify-json/uiw": "^1.2.1", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.5", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "sass-embedded": "^1.85.0", "typescript": "~5.7.2", "unocss": "^66.0.0", "unplugin-auto-import": "^19.1.0", "unplugin-vue-components": "^28.4.0", "vite": "^6.1.0", "vue-tsc": "^2.2.0"}}