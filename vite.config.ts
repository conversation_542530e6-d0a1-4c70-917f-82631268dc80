import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { defineConfig } from 'vite'
import UnoCSS from 'unocss/vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import fs from 'fs'

export default defineConfig({
  plugins: [
    vue(),
    UnoCSS(),
    AutoImport({
      resolvers: [ElementPlusResolver()]
    }),
    Components({
      resolvers: [ElementPlusResolver()]
    }),
    {
      name: 'copy-txt-file',
      closeBundle() {
        // 读取源文件
        const sourceFile = resolve(__dirname, 'TZnHRusHLq.txt')
        const targetFile = resolve(__dirname, 'dist/TZnHRusHLq.txt')
        
        if (fs.existsSync(sourceFile)) {
          // 确保 dist 目录存在
          if (!fs.existsSync('dist')) {
            fs.mkdirSync('dist')
          }
          // 复制文件
          fs.copyFileSync(sourceFile, targetFile)
          console.log('Successfully copied TZnHRusHLq.txt to dist folder')
        } else {
          console.warn('Warning: TZnHRusHLq.txt not found in root directory')
        }
      }
    }
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    host: true,
    port: 5173,
    proxy: {
      '/api': {
        target: 'https://aih.mypacelab.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
